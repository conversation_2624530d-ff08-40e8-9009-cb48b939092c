# 资源中心-证书模板数据库表结构

根据 `src\views\infra\ResourceCenter\CertificateTemplate` 目录下的页面分析，生成以下MySQL建表SQL：

## 1. 证书模板主表 (certificate_template)

```sql
CREATE TABLE `certificate_template` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `type` VARCHAR(50) NOT NULL COMMENT '证书类型：training-培训证书，completion-结业证书，skill-技能证书',
  `description` TEXT COMMENT '模板描述',
  `background` VARCHAR(255) COMMENT '背景图片文件名',
  `background_url` VARCHAR(500) COMMENT '背景图片完整URL地址',
  `course` VARCHAR(100) COMMENT '适用课程名称',
  `status` VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，active-启用中，inactive-已停用',
  `html_content` LONGTEXT COMMENT 'HTML模板内容，支持占位符变量',
  `preview_url` VARCHAR(500) COMMENT '模板预览图URL',
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_type` (`type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_course` (`course`),
  INDEX `idx_create_time` (`create_time`),
  UNIQUE KEY `uk_tenant_name` (`tenant_id`, `name`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='证书模板表';
```

## 2. 证书模板字段配置表 (certificate_template_field)

```sql
CREATE TABLE `certificate_template_field` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `template_id` BIGINT NOT NULL COMMENT '证书模板ID，关联certificate_template.id',
  `field_id` VARCHAR(50) NOT NULL COMMENT '字段唯一标识，如field_1',
  `field_type` VARCHAR(30) NOT NULL COMMENT '字段类型：name-学员姓名，code-证书编号，id-身份证，date-发证日期',
  `field_label` VARCHAR(100) NOT NULL COMMENT '字段显示标签，如【学员姓名】',
  `position_x` INT NOT NULL DEFAULT 0 COMMENT '字段X坐标位置（像素）',
  `position_y` INT NOT NULL DEFAULT 0 COMMENT '字段Y坐标位置（像素）',
  `font_size` INT NOT NULL DEFAULT 20 COMMENT '字体大小（像素）',
  `font_color` VARCHAR(20) NOT NULL DEFAULT '#333' COMMENT '字体颜色，十六进制色值',
  `font_family` VARCHAR(50) NOT NULL DEFAULT '微软雅黑' COMMENT '字体族：微软雅黑，黑体，Arial，Times New Roman，宋体',
  `is_selected` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否选中状态，0-未选中，1-选中',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '字段排序顺序',
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_field_type` (`field_type`),
  INDEX `idx_sort_order` (`sort_order`),
  UNIQUE KEY `uk_template_field` (`template_id`, `field_id`, `deleted`),
  FOREIGN KEY `fk_template_field` (`template_id`) REFERENCES `certificate_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='证书模板字段配置表';
```

## 3. 证书模板统计表 (certificate_template_stats)

```sql
CREATE TABLE `certificate_template_stats` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `template_id` BIGINT NOT NULL COMMENT '证书模板ID，关联certificate_template.id',
  `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT '模板总使用次数',
  `monthly_usage_count` INT NOT NULL DEFAULT 0 COMMENT '本月使用次数',
  `last_used_time` DATETIME COMMENT '最后使用时间',
  `stats_date` DATE NOT NULL COMMENT '统计日期',
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_stats_date` (`stats_date`),
  INDEX `idx_last_used_time` (`last_used_time`),
  UNIQUE KEY `uk_template_stats_date` (`template_id`, `stats_date`, `deleted`),
  FOREIGN KEY `fk_template_stats` (`template_id`) REFERENCES `certificate_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='证书模板统计表';
```

## 4. 证书模板版本历史表 (certificate_template_version)

```sql
CREATE TABLE `certificate_template_version` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `template_id` BIGINT NOT NULL COMMENT '证书模板ID，关联certificate_template.id',
  `version_number` VARCHAR(20) NOT NULL COMMENT '版本号，如v1.0.0',
  `version_name` VARCHAR(100) COMMENT '版本名称',
  `change_description` TEXT COMMENT '版本变更说明',
  `template_snapshot` LONGTEXT COMMENT '模板快照数据（JSON格式）',
  `is_current_version` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否当前版本，0-否，1-是',
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_version_number` (`version_number`),
  INDEX `idx_is_current_version` (`is_current_version`),
  INDEX `idx_create_time` (`create_time`),
  UNIQUE KEY `uk_template_version` (`template_id`, `version_number`, `deleted`),
  FOREIGN KEY `fk_template_version` (`template_id`) REFERENCES `certificate_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='证书模板版本历史表';
```

## 表结构说明

### 主要特性：

1. **证书模板主表**：存储模板基本信息，包括名称、类型、背景图、HTML内容等
2. **字段配置表**：存储模板中动态字段的位置、样式等配置信息
3. **统计表**：记录模板使用情况，便于分析热门模板
4. **版本历史表**：保存模板的历史版本，支持版本回退

### 字段类型枚举：

- **证书类型**：training(培训证书)、completion(结业证书)、skill(技能证书)
- **模板状态**：draft(草稿)、active(启用中)、inactive(已停用)
- **字段类型**：name(学员姓名)、code(证书编号)、id(身份证)、date(发证日期)
- **字体族**：微软雅黑、黑体、Arial、Times New Roman、宋体

### 索引设计：

- 租户隔离索引：`idx_tenant_id`
- 业务查询索引：类型、状态、课程等
- 唯一性约束：租户+模板名称、模板+字段ID等
- 外键约束：保证数据一致性

### 扩展性考虑：

- 支持多租户架构
- 字段配置独立存储，便于扩展新字段类型
- HTML模板内容支持复杂布局
- 版本管理支持模板演进

### 数据示例：

#### 证书模板主表示例数据：

```sql
INSERT INTO `certificate_template` (
  `tenant_id`, `name`, `type`, `description`, `background`, `background_url`,
  `course`, `status`, `html_content`, `creator`, `creator_name`
) VALUES (
  1, '新媒体初级培训证书模板', 'training', '新媒体初级培训证书模板描述',
  'default-bg.jpg', 'https://example.com/bg/default-bg.jpg',
  '新媒体初级培训', 'active',
  '<div class="certificate-template" style="width: 900px; height: 600px; position: relative; background: url({{backgroundUrl}}) center/cover;">
    <div style="position: absolute; left: 350px; top: 200px; font-size: 32px; color: #333; font-family: 微软雅黑;">{{studentName}}</div>
    <div style="position: absolute; left: 350px; top: 400px; font-size: 16px; color: #666; font-family: 微软雅黑;">{{issueDate}}</div>
  </div>',
  'admin', '管理员'
);
```

#### 证书模板字段配置表示例数据：

```sql
INSERT INTO `certificate_template_field` (
  `tenant_id`, `template_id`, `field_id`, `field_type`, `field_label`,
  `position_x`, `position_y`, `font_size`, `font_color`, `font_family`, `sort_order`,
  `creator`, `creator_name`
) VALUES
(1, 1, 'field_1', 'name', '【学员姓名】', 350, 200, 32, '#333', '微软雅黑', 1, 'admin', '管理员'),
(1, 1, 'field_2', 'date', '【发证日期】', 350, 400, 16, '#666', '微软雅黑', 2, 'admin', '管理员'),
(1, 1, 'field_3', 'code', '【证书编号】', 700, 550, 14, '#999', '微软雅黑', 3, 'admin', '管理员');
```

### 业务逻辑说明：

1. **模板创建流程**：

   - 用户在前端拖拽字段到证书预览区域
   - 系统记录字段位置、样式等信息到字段配置表
   - 根据字段配置自动生成HTML模板内容
   - 保存模板基本信息到主表

2. **模板使用统计**：

   - 每次使用模板生成证书时，更新统计表数据
   - 支持按日期统计使用次数
   - 便于分析热门模板和使用趋势

3. **版本管理**：

   - 每次修改模板时，自动创建新版本记录
   - 保存完整的模板快照数据
   - 支持版本回退和对比功能

4. **多租户支持**：
   - 所有表都包含tenant_id字段
   - 通过租户ID实现数据隔离
   - 支持SaaS多租户架构
