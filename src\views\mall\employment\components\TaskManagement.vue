<!--
  页面名称：任务管理
  功能描述：展示客户服务与保障工单，支持筛选、分页、处理等操作
-->
<template>
  <div class="task-management">
    <!-- 工单类型标签页 -->
    <el-tabs v-model="activeTaskType" class="task-type-tabs">
      <el-tab-pane label="全部工单" name="all">
        <span class="tab-badge">12</span>
      </el-tab-pane>
      <el-tab-pane label="投诉" name="complaint">
        <span class="tab-badge">2</span>
      </el-tab-pane>
      <el-tab-pane label="换人申请" name="replacement">
        <span class="tab-badge">2</span>
      </el-tab-pane>
      <el-tab-pane label="退款申请" name="refund">
        <span class="tab-badge">1</span>
      </el-tab-pane>
      <el-tab-pane label="返工申请" name="rework">
        <span class="tab-badge">1</span>
      </el-tab-pane>
      <el-tab-pane label="请假/顶岗" name="leave">
        <span class="tab-badge">2</span>
      </el-tab-pane>
      <el-tab-pane label="离职申请" name="resignation">
        <span class="tab-badge">3</span>
      </el-tab-pane>
      <el-tab-pane label="其他" name="other">
        <span class="tab-badge">1</span>
      </el-tab-pane>
    </el-tabs>

    <!-- 筛选栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="工单状态：">
        <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" :value="''" />
          <el-option label="待处理" value="pending" />
          <el-option label="处理中" value="processing" />
          <el-option label="已解决" value="resolved" />
          <el-option label="已关闭" value="closed" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度：">
        <el-select v-model="searchForm.priority" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" :value="''" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      <el-form-item label="关联机构：">
        <el-input
          v-model="searchForm.agency"
          placeholder="输入机构名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工单列表 -->
    <div class="task-list">
      <div class="list-header">
        <div class="list-cell">工单号</div>
        <div class="list-cell">工单类型</div>
        <div class="list-cell">关联订单/阿姨</div>
        <div class="list-cell">申请方</div>
        <div class="list-cell">关联机构</div>
        <div class="list-cell">紧急度</div>
        <div class="list-cell">工单状态</div>
        <div class="list-cell">创建时间</div>
        <div class="list-cell">处理人</div>
        <div class="list-cell">操作</div>
      </div>

      <div class="list-body">
        <div v-for="task in tableData" :key="task.id" class="list-row">
          <div class="list-cell">
            <small>{{ task.taskNo }}</small>
          </div>
          <div class="list-cell">
            <el-tag :type="getTaskTypeTag(task.type)" size="small">
              {{ task.typeText }}
            </el-tag>
          </div>
          <div class="list-cell">
            <strong>{{ task.orderNo }}</strong>
            <br />
            <small v-if="task.practitioner">被投诉方: {{ task.practitioner }}</small>
          </div>
          <div class="list-cell">{{ task.applicant }}</div>
          <div class="list-cell">{{ task.agency }}</div>
          <div class="list-cell">
            <el-tag :type="getPriorityTag(task.priority)" size="small">
              {{ task.priorityText }}
            </el-tag>
          </div>
          <div class="list-cell">
            <el-tag :type="getStatusTag(task.status)" size="small">
              {{ task.statusText }}
            </el-tag>
          </div>
          <div class="list-cell">{{ formatDate(task.createTime) }}</div>
          <div class="list-cell">{{ task.handler || '-' }}</div>
          <div class="list-cell">
            <el-button size="small" @click="viewTaskDetail(task)"> 查看详情 </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getTaskList } from '@/api/mall/employment/task'
// mock数据引入
import { mockTaskData } from '@/api/mall/employment/mockData'

/** 当前激活的工单类型 */
const activeTaskType = ref('all')

/** 搜索表单数据 */
const searchForm = reactive({
  status: '',
  priority: '',
  agency: ''
})

/** 表格数据 */
const tableData = ref<any[]>([])

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 获取工单列表 */
const fetchList = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      type: activeTaskType.value === 'all' ? '' : activeTaskType.value,
      ...searchForm
    }
    if (import.meta.env.DEV) {
      // 开发环境下直接用mock数据，并做字段适配
      tableData.value = (mockTaskData.list || []).map((item) => ({
        ...item,
        taskNo: item.id,
        typeText:
          {
            complaint: '投诉',
            replacement: '换人申请',
            refund: '退款申请',
            rework: '返工申请',
            leave: '请假/顶岗',
            resignation: '离职申请',
            other: '其他'
          }[item.type] || '其他',
        orderNo: item.relatedOrder,
        practitioner: item.relatedPractitioner,
        priorityText:
          {
            high: '高',
            medium: '中',
            low: '低'
          }[item.urgency] || '低',
        priority: item.urgency,
        statusText:
          {
            pending: '待处理',
            processing: '处理中',
            resolved: '已解决',
            closed: '已关闭'
          }[item.status] || '待处理',
        handler: item.handler
      }))
      pagination.total = mockTaskData.total
    } else {
      const res = await getTaskList(params)
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error('获取工单列表失败:', error)
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    status: '',
    priority: '',
    agency: ''
  })
  pagination.page = 1
  fetchList()
}

/** 查看工单详情 */
const viewTaskDetail = (task: any) => {
  console.log('查看工单详情:', task)
}

/** 获取工单类型标签 */
const getTaskTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    complaint: 'danger',
    replacement: 'warning',
    refund: 'info',
    rework: 'warning',
    leave: 'info',
    resignation: 'danger',
    other: 'info'
  }
  return typeMap[type] || 'info'
}

/** 获取紧急度标签 */
const getPriorityTag = (priority: string) => {
  const priorityMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return priorityMap[priority] || 'info'
}

/** 获取状态标签 */
const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.task-management {
  .task-type-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .tab-badge {
    margin-left: 8px;
    background: #e9ecef;
    color: #343a40;
    border-radius: 10px;
    padding: 1px 6px;
    font-size: 11px;
  }

  .search-form {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .task-list {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    margin-bottom: 20px;
  }

  .list-header,
  .list-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
  }

  .list-row:last-child {
    border-bottom: none;
  }

  .list-header {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #343a40;
  }

  .list-row:hover {
    background: #f5f7fa;
  }

  .list-cell {
    padding: 0 10px;
    flex: 1;
  }

  .list-cell:first-child {
    flex: 0 0 120px;
  }
  .list-cell:nth-child(2) {
    flex: 0 0 90px;
  }
  .list-cell:nth-child(3) {
    flex: 1 1 18%;
  }
  .list-cell:nth-child(4) {
    flex: 1 1 12%;
  }
  .list-cell:nth-child(5) {
    flex: 1 1 15%;
  }
  .list-cell:nth-child(6) {
    flex: 0 0 80px;
  }
  .list-cell:nth-child(7) {
    flex: 0 0 90px;
  }
  .list-cell:nth-child(8) {
    flex: 1 1 12%;
  }
  .list-cell:nth-child(9) {
    flex: 1 1 10%;
  }
  .list-cell:last-child {
    flex: 0 0 150px;
    text-align: right;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }
}
</style>
