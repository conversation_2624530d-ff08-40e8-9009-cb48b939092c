import request from '@/config/axios'

/**
 * 任务管理API接口
 */

// 任务信息接口
export interface Task {
  id: string
  type: 'complaint' | 'replacement' | 'refund' | 'rework' | 'leave' | 'resignation' | 'other'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  assignee: string
  agencyId: string
  agencyName: string
  customerName: string
  customerPhone: string
  createTime: string
  updateTime: string
  dueTime: string
  completedTime?: string
}

// 查询参数接口
export interface TaskQueryParams {
  type?: string
  status?: string
  priority?: string
  agencyId?: string
  assignee?: string
  keyword?: string
  pageNum: number
  pageSize: number
}

// 新增任务参数接口
export interface CreateTaskParams {
  type: 'complaint' | 'replacement' | 'refund' | 'rework' | 'leave' | 'resignation' | 'other'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignee: string
  agencyId: string
  customerName: string
  customerPhone: string
  dueTime: string
}

// 更新任务参数接口
export interface UpdateTaskParams {
  id: string
  title?: string
  description?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  status?: 'pending' | 'processing' | 'completed' | 'cancelled'
  assignee?: string
  dueTime?: string
}

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns Promise<{ list: Task[], total: number }>
 */
export function getTaskList(params: TaskQueryParams) {
  return request.get({
    url: '/mall/employment/task/list',
    params
  })
}

/**
 * 获取任务详情
 * @param id 任务ID
 * @returns Promise<Task>
 */
export function getTaskDetail(id: string) {
  return request.get({
    url: `/mall/employment/task/${id}`
  })
}

/**
 * 新增任务
 * @param data 任务信息
 * @returns Promise<void>
 */
export function createTask(data: CreateTaskParams) {
  return request.post({
    url: '/mall/employment/task',
    data
  })
}

/**
 * 更新任务
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateTask(data: UpdateTaskParams) {
  return request.put({
    url: `/mall/employment/task/${data.id}`,
    data
  })
}

/**
 * 删除任务
 * @param id 任务ID
 * @returns Promise<void>
 */
export function deleteTask(id: string) {
  return request.delete({
    url: `/mall/employment/task/${id}`
  })
}

/**
 * 更新任务状态
 * @param id 任务ID
 * @param status 状态
 * @returns Promise<void>
 */
export function updateTaskStatus(
  id: string,
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
) {
  return request.put({
    url: `/mall/employment/task/${id}/status`,
    data: { status }
  })
}

/**
 * 分配任务
 * @param id 任务ID
 * @param assignee 负责人
 * @returns Promise<void>
 */
export function assignTask(id: string, assignee: string) {
  return request.put({
    url: `/mall/employment/task/${id}/assign`,
    data: { assignee }
  })
}

/**
 * 完成任务
 * @param id 任务ID
 * @param result 完成结果
 * @returns Promise<void>
 */
export function completeTask(id: string, result: string) {
  return request.put({
    url: `/mall/employment/task/${id}/complete`,
    data: { result }
  })
}

/**
 * 导出任务列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportTaskList(params: Omit<TaskQueryParams, 'pageNum' | 'pageSize'>) {
  return request.download({
    url: '/mall/employment/task/export',
    params
  })
}
