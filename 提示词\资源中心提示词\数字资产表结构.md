-- 1. 数字资产主表（课程主表）
CREATE TABLE `publicbiz_digital_asset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '课程名称',
  `category` varchar(64) NOT NULL DEFAULT '' COMMENT '课程分类',
  `status` varchar(32) NOT NULL DEFAULT '' COMMENT '课程状态',
  `teacher` varchar(64) DEFAULT '' COMMENT '关联讲师',
  `merchant` varchar(64) DEFAULT '' COMMENT '收款商户',
  `business_module` varchar(64) DEFAULT '' COMMENT '所属业务板块',
  `location` varchar(128) DEFAULT '' COMMENT '上课地点',
  `schedule` varchar(128) DEFAULT '' COMMENT '排期',
  `total` int DEFAULT NULL COMMENT '总名额',
  `enrolled` int DEFAULT NULL COMMENT '已报名人数',
  `description` text COMMENT '课程详细介绍',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数字资产/课程';

-- 2. 课程附件表
CREATE TABLE `publicbiz_digital_asset_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `asset_id` bigint(20) NOT NULL COMMENT '课程ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '附件名称',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '附件类型（文档/音频/视频/其它）',
  `file_url` varchar(512) NOT NULL DEFAULT '' COMMENT '附件URL',
  `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '原始文件名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `creator` varchar(64) DEFAULT '' COMMENT '上传人',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_asset_id` (`asset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数字资产/课程附件';
