-- 新增表信息
服务套餐主表：publicbiz_service_package
服务套餐轮播图表：publicbiz_package_carousel
服务套餐特色标签表：publicbiz_package_feature

-- 阿姨管理相关表
阿姨基本信息表：publicbiz_practitioner
阿姨资质文件表：publicbiz_practitioner_qualification
阿姨服务记录表：publicbiz_practitioner_service_record
阿姨评级记录表：publicbiz_practitioner_rating_record



-- 服务套餐主表
CREATE TABLE `publicbiz_service_package` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `name` VARCHAR(200) NOT NULL COMMENT '套餐名称',
  `category` VARCHAR(50) NOT NULL COMMENT '服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理',
  `thumbnail` VARCHAR(500) COMMENT '套餐主图URL',
  `price` DECIMAL(10,2) NOT NULL COMMENT '套餐价格',
  `original_price` DECIMAL(10,2) COMMENT '原价',
  `unit` VARCHAR(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_duration` VARCHAR(100) COMMENT '服务时长，如：4小时、26天、90天',
  `package_type` VARCHAR(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `task_split_rule` VARCHAR(200) COMMENT '任务拆分规则',
  `service_description` TEXT COMMENT '服务描述，建议100-200字',
  `service_details` LONGTEXT COMMENT '详细服务内容，富文本格式',
  `service_process` LONGTEXT COMMENT '服务流程，富文本格式',
  `purchase_notice` TEXT COMMENT '购买须知',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active-已上架/pending-待上架/deleted-回收站',
  
  -- 预约配置字段
  `advance_booking_days` INT NOT NULL DEFAULT 1 COMMENT '预约时间范围：1-提前1天/3-提前3天/7-提前7天',
  `time_selection_mode` VARCHAR(20) NOT NULL DEFAULT '固定时间' COMMENT '时间选择模式：固定时间/灵活时间',
  `appointment_mode` VARCHAR(30) NOT NULL DEFAULT '开始日期预约' COMMENT '预约模式：开始日期预约/一次性预约全部服务次数',
  `service_start_time` VARCHAR(50) NOT NULL DEFAULT '下单后3天内开始' COMMENT '服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始',
  `address_setting` VARCHAR(30) NOT NULL DEFAULT '固定地址' COMMENT '地址设置：固定地址/可变更地址',
  `max_booking_days` INT NOT NULL DEFAULT 30 COMMENT '最大预约天数',
  `cancellation_policy` VARCHAR(500) COMMENT '取消政策',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_package_type` (`package_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐主表';

-- 服务套餐表字段补充脚本
-- 为就业服务-服务套餐模块添加审核状态、所属机构ID和所属机构名称字段

-- 1. 添加审核状态字段
ALTER TABLE `publicbiz_service_package` 
ADD COLUMN `audit_status` VARCHAR(20) NOT NULL DEFAULT 'auditing' COMMENT '审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝';

-- 2. 添加所属机构ID字段
ALTER TABLE `publicbiz_service_package` 
ADD COLUMN `agency_id` BIGINT(20) NULL COMMENT '所属机构ID';


-- 3. 添加所属机构名称字段
ALTER TABLE `publicbiz_service_package` 
ADD COLUMN `agency_name` VARCHAR(100) NULL COMMENT '所属机构名称';

-- 1. 添加拒绝原因字段
ALTER TABLE `publicbiz_service_package`
ADD COLUMN `reject_reason` TEXT NULL COMMENT '拒绝原因，审核拒绝时填写';

-- 4. 为所属机构ID添加索引以提高查询性能
CREATE INDEX `idx_agency_id` ON `publicbiz_service_package` (`agency_id`);

-- 5. 为审核状态添加索引以提高查询性能
CREATE INDEX `idx_audit_status` ON `publicbiz_service_package` (`audit_status`);

-- 6. 更新现有数据的审核状态（可选）
-- 将已上架的套餐设置为已通过审核
UPDATE `publicbiz_service_package` SET `audit_status` = 'approved' WHERE `status` = 'active';

-- 将待上架的套餐设置为审核中
UPDATE `publicbiz_service_package` SET `audit_status` = 'auditing' WHERE `status` = 'pending';

-- 将回收站的套餐设置为已拒绝
UPDATE `publicbiz_service_package` SET `audit_status` = 'rejected' WHERE `status` = 'deleted';


-- 8. 验证字段添加是否成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'publicbiz_service_package' 
AND COLUMN_NAME IN ('audit_status', 'agency_id', 'agency_name');


-- 服务套餐轮播图表
CREATE TABLE `publicbiz_package_carousel` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '轮播图URL',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐轮播图表';


-- 服务套餐特色标签表
CREATE TABLE `publicbiz_package_feature` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `feature_name` VARCHAR(100) NOT NULL COMMENT '特色标签名称',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_feature_name` (`feature_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐特色标签表';

-- ==================== 阿姨管理相关表 ====================

-- 阿姨基本信息表
CREATE TABLE `publicbiz_practitioner` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 基本信息字段
  `name` VARCHAR(50) NOT NULL COMMENT '阿姨姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号，用于登录',
  `id_card` VARCHAR(18) NOT NULL COMMENT '身份证号',
  `hometown` VARCHAR(100) COMMENT '籍贯，如：四川成都',
  `age` INT COMMENT '年龄',
  `gender` VARCHAR(10) COMMENT '性别：male-男/female-女',
  `avatar` VARCHAR(500) COMMENT '头像URL',
  
  -- 服务信息字段
  `service_type` VARCHAR(50) NOT NULL COMMENT '主要服务类型：月嫂/育儿嫂/保洁/护工',
  `experience_years` INT NOT NULL COMMENT '从业年限',
  `platform_status` VARCHAR(20) NOT NULL DEFAULT 'cooperating' COMMENT '平台状态：cooperating-合作中/terminated-已解约',
  `rating` DECIMAL(2,1) NOT NULL DEFAULT 4.5 COMMENT '评级，1.0-5.0',
  
  -- 机构关联字段
  `agency_id` BIGINT COMMENT '所属机构ID',
  `agency_name` VARCHAR(200) COMMENT '所属机构名称',
  
  -- 状态字段
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `current_status` VARCHAR(50) COMMENT '当前状态：服务中/待岗/休假中',
  `current_order_id` VARCHAR(50) COMMENT '当前服务订单ID',
  
  -- 统计字段
  `total_orders` INT NOT NULL DEFAULT 0 COMMENT '累计服务单数',
  `total_income` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计收入',
  `customer_satisfaction` DECIMAL(3,1) COMMENT '客户满意度评分',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_id_card` (`id_card`),
  KEY `idx_service_type` (`service_type`),
  KEY `idx_platform_status` (`platform_status`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_id_card` (`id_card`)
) COMMENT='阿姨基本信息表';

-- 阿姨资质文件表
CREATE TABLE `publicbiz_practitioner_qualification` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `practitioner_id` BIGINT NOT NULL COMMENT '阿姨ID',
  `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` VARCHAR(200) NOT NULL COMMENT '文件名',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_extension` VARCHAR(20) COMMENT '文件扩展名',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-有效，0-无效',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_practitioner_id` (`practitioner_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='阿姨资质文件表';

-- 阿姨服务记录表
CREATE TABLE `publicbiz_practitioner_service_record` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `practitioner_id` BIGINT NOT NULL COMMENT '阿姨ID',
  `order_id` VARCHAR(50) NOT NULL COMMENT '订单ID',
  `customer_id` BIGINT COMMENT '客户ID',
  `customer_name` VARCHAR(50) COMMENT '客户姓名',
  `service_type` VARCHAR(50) NOT NULL COMMENT '服务类型',
  `service_start_time` DATETIME COMMENT '服务开始时间',
  `service_end_time` DATETIME COMMENT '服务结束时间',
  `service_duration` VARCHAR(50) COMMENT '服务时长',
  `service_address` VARCHAR(500) COMMENT '服务地址',
  `service_amount` DECIMAL(10,2) NOT NULL COMMENT '服务金额',
  `practitioner_income` DECIMAL(10,2) COMMENT '阿姨收入',
  `platform_income` DECIMAL(10,2) COMMENT '平台收入',
  `order_status` VARCHAR(20) NOT NULL COMMENT '订单状态：pending-待确认/confirmed-已确认/in_progress-服务中/completed-已完成/cancelled-已取消',
  `customer_rating` DECIMAL(2,1) COMMENT '客户评分',
  `customer_comment` TEXT COMMENT '客户评价',
  `remark` TEXT COMMENT '备注',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_practitioner_id` (`practitioner_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_service_type` (`service_type`),
  KEY `idx_service_start_time` (`service_start_time`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='阿姨服务记录表';

-- 阿姨评级记录表
CREATE TABLE `publicbiz_practitioner_rating_record` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `practitioner_id` BIGINT NOT NULL COMMENT '阿姨ID',
  `rating_type` VARCHAR(20) NOT NULL COMMENT '评级类型：customer-客户评价/platform-平台评级/system-系统评级',
  `old_rating` DECIMAL(2,1) COMMENT '原评级',
  `new_rating` DECIMAL(2,1) NOT NULL COMMENT '新评级',
  `rating_change` DECIMAL(2,1) COMMENT '评级变化',
  `rating_reason` VARCHAR(500) COMMENT '评级原因',
  `evaluator_id` BIGINT COMMENT '评价人ID',
  `evaluator_name` VARCHAR(50) COMMENT '评价人姓名',
  `evaluator_type` VARCHAR(20) COMMENT '评价人类型：customer-客户/admin-管理员/system-系统',
  `related_order_id` VARCHAR(50) COMMENT '关联订单ID',
  `remark` TEXT COMMENT '备注',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_practitioner_id` (`practitioner_id`),
  KEY `idx_rating_type` (`rating_type`),
  KEY `idx_evaluator_id` (`evaluator_id`),
  KEY `idx_related_order_id` (`related_order_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='阿姨评级记录表';