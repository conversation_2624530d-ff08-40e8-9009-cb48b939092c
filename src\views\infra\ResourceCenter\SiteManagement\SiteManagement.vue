<!--
  页面名称：场地管理
  功能描述：展示场地列表，支持统计、搜索、分页、编辑、删除、详情、批量导入等操作
-->
<template>
  <AddSite v-if="addSiteVisible" :visible="addSiteVisible" :data="editRow || {}" @close="closeAddSite" @submit="handleAddSiteSubmit" />
  <SiteAppointment v-if="appointmentVisible" :visible="appointmentVisible" :siteInfo="appointmentRow || {}" :reservedList="reservedList" @close="closeAppointment" @submit="handleAppointmentSubmit" />
  <SiteOptLog v-if="optLogVisible" :visible="optLogVisible" :siteName="optLogSiteName || ''" :logs="optLogList" @close="closeOptLog" />
  <div class="site-management">
    <!-- 新增按钮区 -->
    <div class="site-top-btns">
      <el-button :type="mainTab==='list'?'primary':'default'" icon="el-icon-menu" class="site-top-btn" @click="mainTab='list'">场地列表</el-button>
      <el-button :type="mainTab==='board'?'primary':'default'" icon="el-icon-date" class="site-top-btn" @click="mainTab='board'">排期看板</el-button>
    </div>
    <!-- 顶部统计卡片和搜索栏，仅在场地列表视图显示 -->
    <template v-if="mainTab==='list'">
      <!-- 顶部统计卡片（合作伙伴风格） -->
      <el-row :gutter="20" class="mb-4">
        <el-col :xs="24" :sm="12" :md="6" v-for="(card, idx) in statCards" :key="card.label">
          <el-card :class="`stat-card stat-bg${idx+1}`">
            <div class="stat-card-inner">
              <el-icon :class="`stat-icon stat-icon${idx+1}`"><component :is="card.icon" /></el-icon>
              <div>
                <div class="stat-num">{{ card.value }}</div>
                <div class="stat-label">{{ card.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 搜索栏 -->
      <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
        <el-form-item label="校区">
          <el-select v-model="searchForm.campus" placeholder="全部校区" clearable style="width: 120px">
            <el-option v-for="item in campusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="场地类型">
          <el-select v-model="searchForm.type" placeholder="全部类型" clearable style="width: 120px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable style="width: 120px">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="容量">
          <el-select v-model="searchForm.capacity" placeholder="全部容量" clearable style="width: 120px">
            <el-option v-for="item in capacityOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchForm.keyword" placeholder="搜索场地名称..." clearable style="width: 180px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">筛选</el-button>
        </el-form-item>
      </el-form>
    </template>

    <transition name="fade-transform" mode="out-in">
      <div v-if="mainTab==='list'" key="list">
        <!-- 场地列表表格 -->
        <el-table :data="tableData" border stripe style="width: 100%; margin-top: 10px;" :header-cell-style="{background:'#fafbfc'}">
          <el-table-column prop="name" label="场地名称" min-width="160" />
          <el-table-column prop="campus" label="校区" min-width="100" />
          <el-table-column prop="type" label="场地类型" min-width="100" />
          <el-table-column prop="seat" label="总座位数" min-width="100">
            <template #default="scope">
              <div>
                <span style="font-weight:bold">{{ scope.row.seat }}</span>
                <span v-if="scope.row.seatDetail" style="color:#999;font-size:12px;">（{{ scope.row.seatDetail }}）</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="location" label="位置" min-width="120" />
          <el-table-column prop="equipment" label="设备配置" min-width="200" />
          <el-table-column prop="status" label="状态" min-width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.status==='可用'" type="success">可用</el-tag>
              <el-tag v-else-if="scope.row.status==='已预约'" type="warning">已预约</el-tag>
              <el-tag v-else-if="scope.row.status==='维护中'" type="info">维护中</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="manager" label="负责人" min-width="120">
            <template #default="scope">
              <div>{{ scope.row.manager }}<br /><span style="color:#999;font-size:12px;">{{ scope.row.managerPhone }}</span></div>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100" fixed="right">
            <template #default="scope">
              <el-dropdown @command="(cmd) => handleCommand(scope.row, cmd)">
                <el-button size="small" type="primary">
                  操作 <el-icon class="el-icon--right"><i class="el-icon-arrow-down"></i></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="appointment">预约</el-dropdown-item>
                    <el-dropdown-item command="log">操作日志</el-dropdown-item>
                    <el-dropdown-item command="delete" style="color: #ff4d4f;">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10,20,50,100]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          style="margin: 16px 0; text-align:right;"
          @current-change="fetchList"
          @size-change="fetchList"
        />
      </div>
      <SchedulingBoard v-else key="board" />
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, markRaw } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Calendar, Check, Tools } from '@element-plus/icons-vue'
import AddSite from './components/AddSite.vue'
import SiteAppointment from './components/SiteAppointment.vue'
import SiteOptLog from './components/SiteOptLog.vue'
import SchedulingBoard from './components/SchedulingBoard.vue'
// import { getSitePage, getSiteStats } from '@/api/infra/ResourceCenter/SiteManagement' // 预留API
import { getAreaTree } from '@/api/system/area' // 校区/地区树

/** 统计卡片数据 */
const statCards = ref([
  { label: '场地总数', value: 8, icon: markRaw(Check) },
  { label: '可用场地', value: 5, icon: markRaw(Check) },
  { label: '已预约', value: 1, icon: markRaw(Calendar) },
  { label: '维护中', value: 1, icon: markRaw(Tools) },
])

/** 搜索表单数据 */
const searchForm = ref({
  campus: '',
  type: '',
  status: '',
  capacity: '',
  keyword: '',
})

/** 校区选项（可用getAreaTree接口动态获取） */
const campusOptions = ref([
  { label: '全部校区', value: '' },
  { label: '总部校区', value: '总部校区' },
  { label: '华东分校', value: '华东分校' },
])

/** 场地类型选项 */
const typeOptions = ref([
  { label: '全部类型', value: '' },
  { label: '培训教室', value: '培训教室' },
  { label: '多功能厅', value: '多功能厅' },
  { label: '会议室', value: '会议室' },
  { label: '实训室', value: '实训室' },
  { label: '考试场地', value: '考试场地' },
  { label: '研讨室', value: '研讨室' },
])

/** 状态选项 */
const statusOptions = ref([
  { label: '全部状态', value: '' },
  { label: '可用', value: '可用' },
  { label: '已预约', value: '已预约' },
  { label: '维护中', value: '维护中' },
])

/** 容量选项（可根据实际需求调整） */
const capacityOptions = ref([
  { label: '全部容量', value: '' },
  { label: '20座以下', value: '20' },
  { label: '21-50座', value: '50' },
  { label: '51-100座', value: '100' },
  { label: '100座以上', value: '101' },
])

/** 表格数据 */
const tableData = ref([
  // 示例数据，后续用接口数据替换
  {
    name: '总部A座101教室', campus: '总部校区', type: '培训教室', seat: 50, seatDetail: '普通座:50座', location: 'A座1楼101室', equipment: '投影仪、音响设备、空调、白板', status: '可用', manager: '张老师', managerPhone: '13812345678'
  },
  {
    name: '总部A座201多功能厅', campus: '总部校区', type: '多功能厅', seat: 120, seatDetail: 'VIP座:20座, 普通座:100座', location: 'A座2楼201室', equipment: '舞台灯光、音响系统、投影设备、话筒设备', status: '可用', manager: '李老师', managerPhone: '13823456789'
  },
  {
    name: '总部B座302会议室', campus: '总部校区', type: '会议室', seat: 20, seatDetail: '会议桌:20座', location: 'B座3楼302室', equipment: '会议桌、投影设备、电话会议系统', status: '已预约', manager: '王老师', managerPhone: '13834567890'
  },
  {
    name: '总部C座实训室', campus: '总部校区', type: '实训室', seat: 30, seatDetail: '实训工位:30座', location: 'C座1楼实训区', equipment: '实训设备、安全防护设备、工具箱', status: '维护中', manager: '赵师傅', managerPhone: '13845678901'
  },
  {
    name: '总部A座203考试专用教室', campus: '总部校区', type: '考试场地', seat: 40, seatDetail: '考试桌:40座', location: 'A座2楼203室', equipment: '考试桌椅、监控设备、屏蔽设备、时钟', status: '可用', manager: '陈老师', managerPhone: '13856789012'
  },
  {
    name: '华东分校小型研讨室', campus: '华东分校', type: '研讨室', seat: 8, seatDetail: '', location: 'B座2楼研讨室', equipment: '圆桌、投影设备、白板', status: '可用', manager: '周老师', managerPhone: '13867890123'
  },
])

/** 分页信息 */
const pagination = ref({ page: 1, size: 10, total: 8 })

/** 获取场地列表（预留，实际请调用API） */
const fetchList = async () => {
  // TODO: 调用 getSitePage(searchForm.value, pagination.value) 获取数据
  // tableData.value = res.list
  // pagination.value.total = res.total
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 编辑 */
const addSiteVisible = ref(false)
const editRow = ref({})
const onEdit = (row: any) => {
  editRow.value = { ...row }
  addSiteVisible.value = true
}
const closeAddSite = () => {
  addSiteVisible.value = false
  editRow.value = {}
}
const handleAddSiteSubmit = (data: any) => {
  // TODO: 保存编辑/新增
  closeAddSite()
  ElMessage.success('保存成功')
}

/** 删除 */
const onDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除场地【${row.name}】吗？`, '提示', { type: 'warning' })
    .then(() => {
      ElMessage.success('删除成功（模拟）')
      // TODO: 调用删除接口
    })
    .catch(() => {})
}

/** 详情 */
const onDetail = (row: any) => {
  ElMessage.info('详情功能开发中...')
}

/** 批量导入 */
const onBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const appointmentVisible = ref(false)
const appointmentRow = ref({})
const reservedList = ref<any[]>([
  // 示例预约数据
  {
    activityName: '家政服务员技能培训',
    date: '2024-08-26',
    time: '09:00-17:00',
    type: '培训',
    peopleCount: 35,
    contactName: '张老师',
    contactPhone: '13812345678',
    remark: '需要投影设备和音响'
  }
])
const onAppointment = (row: any) => {
  appointmentRow.value = { ...row }
  appointmentVisible.value = true
}
const closeAppointment = () => {
  appointmentVisible.value = false
  appointmentRow.value = {}
}
const handleAppointmentSubmit = (data: any) => {
  closeAppointment()
  ElMessage.success('预约成功')
}

const optLogVisible = ref(false)
const optLogSiteName = ref('')
const optLogList = ref<any[]>([
  // 示例操作日志
  { time: '2024-01-10', operator: '系统', content: '创建场地 "总部A座101教室"' }
])
const onOptLog = (row: any) => {
  optLogSiteName.value = row.name || ''
  optLogVisible.value = true
}
const closeOptLog = () => {
  optLogVisible.value = false
  optLogSiteName.value = ''
}

const mainTab = ref('list') // 'list' or 'board'

function handleCommand(row: any, command: string) {
  if (command === 'edit') onEdit(row)
  else if (command === 'appointment') onAppointment(row)
  else if (command === 'log') onOptLog(row)
  else if (command === 'delete') onDelete(row)
}

onMounted(() => {
  // fetchList()
  // getAreaTree().then(res => { campusOptions.value = ... })
})
</script>

<style scoped lang="scss">
.site-management {
  padding: 16px;
  min-width: 320px;
  box-sizing: border-box;
}
.site-top-btns {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}
.site-top-btn {
  font-size: 15px;
  border-radius: 6px;
  height: 38px;
  padding: 0 18px;
  font-weight: 500;
  letter-spacing: 1px;
}
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255,255,255,0.3);
}
.stat-icon1 {
  color: #409EFF;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67C23A;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #E6A23C;
  background: #fff5e6;
}
.stat-icon4 {
  color: #F56C6C;
  background: #ffeaea;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #ffeaea 0%, #ffd6d6 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.search-form {
  margin-bottom: 10px;
  flex-wrap: wrap;
  display: flex;
  align-items: center;
}
@media (max-width: 768px) {
  .stat-cards {
    .el-col {
      margin-bottom: 12px;
    }
  }
  .search-form {
    flex-direction: column;
    align-items: flex-start;
    .el-form-item {
      margin-bottom: 8px;
    }
  }
}
.fade-transform-enter-active, .fade-transform-leave-active {
  transition: all 0.3s cubic-bezier(.55,0,.1,1);
}
.fade-transform-enter-from, .fade-transform-leave-to {
  opacity: 0;
  transform: translateY(16px) scale(0.98);
}
</style>
