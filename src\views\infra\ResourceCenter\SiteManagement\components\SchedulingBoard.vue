<template>
  <div class="scheduling-board">
    <!-- 顶部操作区 -->
    <div class="sb-toolbar">
      <div class="sb-toolbar-left">
        <el-button-group>
          <el-button @click="onPrevMonth"><el-icon><i class="el-icon-arrow-left"></i></el-icon> 上月</el-button>
          <el-button @click="onToday">回到今日</el-button>
          <el-button @click="onNextMonth">下月 <el-icon><i class="el-icon-arrow-right"></i></el-icon></el-button>
        </el-button-group>
        <el-date-picker
          v-model="currentDate"
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :clearable="false"
          style="width:120px;"
        />
      </div>
      <div class="sb-toolbar-center">
        <el-button :type="viewType==='month'?'primary':'default'" @click="viewType='month'">月视图</el-button>
      </div>
      <div class="sb-toolbar-right">
        <el-select v-model="campus" placeholder="全部校区" style="width:130px;margin-right:8px;">
          <el-option v-for="item in campusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model="search" placeholder="搜索场地..." style="width:160px;" clearable />
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="sb-main-card">
      <div class="sb-table">
        <div class="sb-table-left">
          <div class="sb-table-header">场地列表</div>
          <div v-for="site in filteredSites" :key="site.id" class="sb-site-row">
            <div class="sb-site-title">{{ site.name }}</div>
            <div class="sb-site-desc">{{ site.campus }} · {{ site.type }} · {{ site.capacity }}座</div>
            <el-button size="small" type="primary" class="sb-site-btn">+预约</el-button>
          </div>
        </div>
        <div class="sb-table-right">
          <div class="sb-calendar-header">
            <div v-for="d in days" :key="d" class="sb-calendar-day">{{ d }}</div>
          </div>
          <div v-for="site in filteredSites" :key="site.id" class="sb-calendar-row">
            <div v-for="d in days" :key="d" class="sb-calendar-cell">
              <div v-if="isReserved(site.id, d)" class="sb-reserved-block"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 获取当前年月（YYYY-MM）
function getCurrentYearMonth() {
  const now = new Date()
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
}

const currentDate = ref(getCurrentYearMonth())
const viewType = ref('month')
const campus = ref('全部校区')
const search = ref('')

const campusOptions = ['全部校区', '总部校区', '华东分校', '华南分校', '华北分校', '西部分校']

const sites = ref([
  { id: 1, name: '总部A座101教室', campus: '总部校区', type: '培训教室', capacity: 50 },
  { id: 2, name: '总部A座201多功能厅', campus: '总部校区', type: '多功能厅', capacity: 120 },
  { id: 3, name: '总部B座302会议室', campus: '总部校区', type: '会议室', capacity: 20 },
  { id: 4, name: '总部C座实训室', campus: '总部校区', type: '实训室', capacity: 30 },
  { id: 5, name: '总部A座203考试专用教室', campus: '总部校区', type: '考试场地', capacity: 40 },
  { id: 6, name: '华东分校小型研讨室', campus: '华东分校', type: '研讨室', capacity: 8 },
  { id: 7, name: '华东分校计算机教室', campus: '华东分校', type: '培训教室', capacity: 35 },
  { id: 8, name: '华南分校大型会议厅', campus: '华南分校', type: '多功能厅', capacity: 200 }
])

const days = ref<number[]>([])
function updateDays() {
  const [year, month] = currentDate.value.split('-').map(Number)
  const lastDay = new Date(year, month, 0).getDate()
  days.value = Array.from({ length: lastDay }, (_, i) => i + 1)
}
updateDays()
watch(currentDate, updateDays)

const reservations = ref([
  // siteId, day
  { siteId: 4, day: 1 }, { siteId: 4, day: 2 }, { siteId: 4, day: 3 }, { siteId: 4, day: 4 },
  { siteId: 4, day: 5 }, { siteId: 4, day: 6 }, { siteId: 4, day: 7 }, { siteId: 4, day: 8 },
  { siteId: 4, day: 9 }, { siteId: 4, day: 10 }, { siteId: 4, day: 11 }, { siteId: 4, day: 12 },
  { siteId: 4, day: 13 }, { siteId: 4, day: 14 }, { siteId: 4, day: 15 }, { siteId: 4, day: 16 },
  { siteId: 4, day: 17 }, { siteId: 4, day: 18 }, { siteId: 4, day: 19 }, { siteId: 4, day: 20 },
  { siteId: 4, day: 21 }, { siteId: 4, day: 22 }, { siteId: 4, day: 23 }, { siteId: 4, day: 24 },
  { siteId: 4, day: 25 }, { siteId: 4, day: 26 }, { siteId: 4, day: 27 }, { siteId: 4, day: 28 },
  { siteId: 4, day: 29 }, { siteId: 4, day: 30 }, { siteId: 4, day: 31 }
])

const filteredSites = computed(() => {
  return sites.value.filter(site =>
    (campus.value === '全部校区' || site.campus === campus.value) &&
    (!search.value || site.name.includes(search.value))
  )
})

const isReserved = (siteId: number, day: number) => {
  return reservations.value.some(r => r.siteId === siteId && r.day === day)
}

const onPrevMonth = () => {
  const [year, month] = currentDate.value.split('-').map(Number)
  let newYear = year
  let newMonth = month - 1
  if (newMonth === 0) {
    newYear -= 1
    newMonth = 12
  }
  currentDate.value = `${newYear}-${String(newMonth).padStart(2, '0')}`
}
const onNextMonth = () => {
  const [year, month] = currentDate.value.split('-').map(Number)
  let newYear = year
  let newMonth = month + 1
  if (newMonth === 13) {
    newYear += 1
    newMonth = 1
  }
  currentDate.value = `${newYear}-${String(newMonth).padStart(2, '0')}`
}
const onToday = () => {
  currentDate.value = getCurrentYearMonth()
}
</script>

<style scoped lang="scss">
.scheduling-board {
  padding: 18px;
  background: #f7f8fa;
}
.sb-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.sb-toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sb-toolbar-center {
  display: flex;
  gap: 8px;
}
.sb-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sb-main-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 18px 0 0 0;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  height: 80vh;
}
.sb-table {
  display: flex;
  align-items: stretch;
  flex: 1;
  min-height: 0;
  height: 100%;
}
.sb-table-left {
  width: 260px;
  border-right: 1px solid #e5e6eb;
  padding: 0 0 0 18px;
  background: #fff;
}
.sb-table-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
}
.sb-site-row {
  margin-bottom: 18px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #f0f0f0;
  position: relative;
}
.sb-site-title {
  font-weight: 500;
  font-size: 15px;
}
.sb-site-desc {
  color: #888;
  font-size: 13px;
  margin: 2px 0 6px 0;
}
.sb-site-btn {
  position: absolute;
  right: 0;
  top: 8px;
  font-size: 13px;
  padding: 2px 12px;
  border-radius: 6px;
}
.sb-table-right {
  flex: 1;
  overflow-x: auto;
  padding-left: 18px;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  min-height: 0;
  height: 100%;
}
.sb-calendar-header {
  display: flex;
  border-bottom: 1px solid #e5e6eb;
  margin-bottom: 2px;
  flex: none;
}
.sb-calendar-day {
  flex: 1 1 0;
  min-width: 36px;
  text-align: center;
  color: #888;
  font-size: 13px;
  padding: 2px 0;
}
.sb-calendar-row {
  display: flex;
  min-height: 32px;
  flex: 1 1 0;
}
.sb-calendar-cell {
  flex: 1 1 0;
  min-width: 36px;
  height: 32px;
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
.sb-reserved-block {
  width: 80%;
  height: 22px;
  background: #e57373;
  border-radius: 5px;
  margin: 0 auto;
}
@media (max-width: 1200px) {
  .sb-main-card { height: auto; min-height: 400px; }
  .sb-table-left { width: 180px; }
  .sb-calendar-day, .sb-calendar-cell { min-width: 24px; }
}
@media (max-width: 900px) {
  .sb-main-card { height: auto; min-height: 300px; }
  .sb-table-left { width: 120px; }
  .sb-calendar-day, .sb-calendar-cell { min-width: 18px; font-size: 11px; }
}
</style>
