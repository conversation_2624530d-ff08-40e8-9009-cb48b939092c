/**
 * 就业服务模块 Mock 数据
 * 包含机构管理、任务管理、轮播图管理、资讯管理等模块的模拟数据
 */

// 机构管理 Mock 数据
export const mockAgencyData = {
  list: [
    {
      id: '88001',
      name: '阳光家政',
      fullName: '阳光家政服务有限公司',
      type: '合作',
      status: 'enabled',
      contact: '张经理',
      phone: '138****5678',
      region: '北京-海淀区',
      joinDate: '2023-01-15',
      rating: 5,
      contractNo: 'HC-************',
      contractPeriod: '2023-01-15 至 2025-01-14',
      deposit: '¥10,000 (已缴纳)',
      renewalReminder: '2024-12-15',
      businessScope: ['月嫂', '育儿嫂', '高端家务'],
      legalPerson: '张三',
      establishDate: '2020-05-10',
      creditCode: '91110108MA01R2A25C',
      registerAddress: '北京市海淀区中关村南大街1号',
      businessAddress: '北京市海淀区中关村南大街1号A座101室',
      cooperationMode: '订单分成',
      ourSigner: '李主管',
      bankAccount: {
        name: '阳光家政服务有限公司',
        bank: '招商银行北京分行中关村支行',
        account: '6225 **** **** 1234'
      },
      settlementCycle: '月结 (每月25日)',
      qualifications: [
        { name: '营业执照.pdf', status: 'verified', url: '#' },
        { name: '人力资源服务许可证.pdf', status: 'verified', url: '#' },
        { name: '开户许可证.jpg', status: 'verified', url: '#' },
        { name: '法人身份证.zip', status: 'unverified', url: null }
      ]
    },
    {
      id: '88002',
      name: 'A家政服务',
      fullName: 'A家政服务有限公司',
      type: '合作',
      status: 'disabled',
      contact: '李女士',
      phone: '139****1122',
      region: '上海-浦东新区',
      joinDate: '2022-11-20',
      rating: 4,
      contractNo: 'HC-********-002',
      contractPeriod: '2022-11-20 至 2024-11-19',
      deposit: '¥8,000 (已缴纳)',
      renewalReminder: '2024-10-20',
      businessScope: ['日常保洁', '深度保洁', '收纳整理'],
      legalPerson: '李四',
      establishDate: '2019-08-15',
      creditCode: '91310115MA1K4B8X2Y',
      registerAddress: '上海市浦东新区张江高科技园区',
      businessAddress: '上海市浦东新区张江高科技园区科苑路88号',
      cooperationMode: '订单分成',
      ourSigner: '王经理',
      bankAccount: {
        name: 'A家政服务有限公司',
        bank: '工商银行上海分行张江支行',
        account: '6222 **** **** 5678'
      },
      settlementCycle: '月结 (每月20日)',
      qualifications: [
        { name: '营业执照.pdf', status: 'verified', url: '#' },
        { name: '人力资源服务许可证.pdf', status: 'verified', url: '#' },
        { name: '开户许可证.jpg', status: 'verified', url: '#' },
        { name: '法人身份证.jpg', status: 'verified', url: '#' }
      ]
    }
  ],
  total: 2
}

// 机构业务数据 Mock 数据
export const mockAgencyBusinessData = {
  coreMetrics: {
    serviceOrders: 88,
    interviewSuccessRate: '75.6%',
    customerSatisfaction: '98.5%',
    complaintRate: '1.2%'
  },
  practitionerMetrics: {
    totalPractitioners: 125,
    newPractitioners: 12,
    lostPractitioners: 3,
    activePractitioners: 89
  },
  financialMetrics: {
    totalOrderAmount: '¥450,800.00',
    ourIncome: '¥45,080.00',
    settledAmount: '¥35,800.00',
    pendingSettlement: '¥9,280.00'
  },
  charts: {
    monthlyOrders: {
      labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
      data: [12, 15, 18, 22, 25, 28]
    },
    categoryDistribution: {
      labels: ['月嫂', '育儿嫂', '日常保洁', '深度保洁', '收纳整理'],
      data: [35, 25, 20, 15, 5]
    },
    qualityTrend: {
      labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
      data: [95, 96, 97, 98, 98.5, 98.5]
    }
  }
}

// 机构旗下阿姨 Mock 数据
export const mockAgencyPractitioners = {
  list: [
    {
      id: 'AY00123',
      name: '李丽',
      rating: 4.9,
      serviceType: '月嫂',
      totalOrders: 35,
      currentStatus: '服务中',
      currentOrder: 'DD20240615',
      platformStatus: 'active',
      phone: '138****1234',
      age: 35,
      experience: '5年',
      skills: ['新生儿护理', '产妇护理', '月子餐制作'],
      certificates: ['月嫂证', '健康证', '育婴师证']
    },
    {
      id: 'AY00124',
      name: '王芳',
      rating: 4.8,
      serviceType: '育儿嫂',
      totalOrders: 28,
      currentStatus: '待岗',
      currentOrder: null,
      platformStatus: 'active',
      phone: '139****5678',
      age: 32,
      experience: '4年',
      skills: ['婴幼儿护理', '早教启蒙', '辅食制作'],
      certificates: ['育婴师证', '健康证']
    },
    {
      id: 'AY00125',
      name: '陈静',
      rating: 4.9,
      serviceType: '保洁',
      totalOrders: 102,
      currentStatus: '休假中',
      currentOrder: null,
      platformStatus: 'pending',
      phone: '137****9012',
      age: 40,
      experience: '8年',
      skills: ['日常保洁', '深度保洁', '收纳整理'],
      certificates: ['健康证']
    },
    {
      id: 'AY00109',
      name: '赵美玲',
      rating: 4.7,
      serviceType: '月嫂',
      totalOrders: 21,
      currentStatus: '-',
      currentOrder: null,
      platformStatus: 'inactive',
      phone: '136****3456',
      age: 38,
      experience: '3年',
      skills: ['新生儿护理', '产妇护理'],
      certificates: ['月嫂证', '健康证']
    }
  ],
  total: 25
}

// 机构激励/处罚记录 Mock 数据
export const mockAgencyRecords = {
  list: [
    {
      id: 'REC001',
      type: 'incentive',
      title: '平台奖励 - 月度优秀机构',
      date: '2024-05-31',
      impact: '+10 信用分',
      reason: '因其卓越的服务质量和客户满意度，被评为5月度优秀合作机构。',
      status: 'completed',
      attachments: []
    },
    {
      id: 'REC002',
      type: 'punishment',
      title: '客户投诉 - 服务态度',
      date: '2024-05-20',
      impact: '-5 信用分, -¥200.00 罚款',
      reason: '客户投诉阿姨服务态度问题，经核查属实。关联订单 20240518008。',
      status: 'pending',
      attachments: ['投诉录音.mp3'],
      relatedOrder: '20240518008'
    },
    {
      id: 'REC003',
      type: 'incentive',
      title: '客户好评 - 超出预期',
      date: '2024-04-15',
      impact: '+5 信用分, +¥50.00 奖励',
      reason: '关联订单 20240410021，客户致电表扬阿姨工作细致，服务超出预期。',
      status: 'completed',
      attachments: [],
      relatedOrder: '20240410021'
    }
  ],
  total: 3
}

// 机构沟通日志 Mock 数据
export const mockAgencyLogs = {
  list: [
    {
      id: 'LOG001',
      type: 'call',
      topic: '月度例行沟通',
      date: '2024-06-10',
      participants: '我方(王经理), 对方(张经理)',
      content:
        '对方反馈近期阿姨资源紧张，希望能加派人手。已告知会尽快协调，并建议对方可以适当提高空闲阿姨的接单奖励金。',
      followUp: '跟进协调结果。',
      status: 'pending',
      attachments: []
    },
    {
      id: 'LOG002',
      type: 'visit',
      topic: '合作续约',
      date: '2024-03-05',
      participants: '我方(李主管), 对方(张经理, 法人张三)',
      content:
        '拜访了该机构，就第二季度的合作续约条款达成初步一致。对方对我们的派单效率表示满意，提出了希望年度返点比例能提高0.5%的诉求。',
      followUp: '2024-03-15前，法务出具新版合同。',
      status: 'completed',
      attachments: ['合同草稿.pdf']
    },
    {
      id: 'LOG003',
      type: 'call',
      topic: '初步对接',
      date: '2023-01-10',
      participants: '我方(系统导入)',
      content:
        '首次与该机构联系人张经理取得联系，介绍了我们的合作模式，对方表示出强烈兴趣，已发送合作资料。',
      followUp: null,
      status: 'completed',
      attachments: ['合作资料.pdf']
    }
  ],
  total: 3
}

// 任务管理 Mock 数据
export const mockTaskData = {
  list: [
    {
      id: 'GD20240627001',
      type: 'complaint',
      relatedOrder: 'DD20240626002',
      relatedPractitioner: '陈静(AY00125)',
      applicant: '雇主: 王先生',
      agency: '阳光家政',
      urgency: 'high',
      status: 'processing',
      createTime: '2024-06-27 09:30',
      handler: '张客服',
      description: '客户投诉阿姨服务态度问题，要求更换阿姨或退款。'
    },
    {
      id: 'GD20240627002',
      type: 'replacement',
      relatedOrder: 'DD20240625001',
      relatedPractitioner: '李丽(AY00123)',
      applicant: '雇主: 李女士',
      agency: '阳光家政',
      urgency: 'medium',
      status: 'pending',
      createTime: '2024-06-27 10:15',
      handler: '王客服',
      description: '客户申请更换阿姨，原阿姨因个人原因无法继续服务。'
    },
    {
      id: 'GD20240627003',
      type: 'refund',
      relatedOrder: 'DD20240620003',
      relatedPractitioner: '王芳(AY00124)',
      applicant: '雇主: 陈先生',
      agency: 'A家政服务',
      urgency: 'low',
      status: 'resolved',
      createTime: '2024-06-27 11:00',
      handler: '李客服',
      description: '客户申请退款，服务未开始，已同意退款申请。'
    }
  ],
  total: 12,
  summary: {
    all: 12,
    complaint: 2,
    replacement: 2,
    refund: 1,
    rework: 1,
    leave: 2,
    resignation: 3,
    other: 1
  }
}

// 轮播图管理 Mock 数据
export const mockCarouselData = {
  list: [
    {
      id: '1',
      title: '专业月嫂服务',
      imageUrl: 'https://picsum.photos/seed/carousel1/800/400',
      linkUrl: '/service/maternity',
      sort: 1,
      status: 'enabled',
      createTime: '2024-06-20 10:00',
      updateTime: '2024-06-20 10:00'
    },
    {
      id: '2',
      title: '日常保洁服务',
      imageUrl: 'https://picsum.photos/seed/carousel2/800/400',
      linkUrl: '/service/cleaning',
      sort: 2,
      status: 'enabled',
      createTime: '2024-06-19 14:30',
      updateTime: '2024-06-19 14:30'
    },
    {
      id: '3',
      title: '深度保洁服务',
      imageUrl: 'https://picsum.photos/seed/carousel3/800/400',
      linkUrl: '/service/deep-cleaning',
      sort: 3,
      status: 'disabled',
      createTime: '2024-06-18 09:15',
      updateTime: '2024-06-18 09:15'
    }
  ],
  total: 3
}

// 资讯管理 Mock 数据
export const mockNewsData = {
  list: [
    {
      id: '1',
      title: '月嫂服务标准升级，提升服务质量',
      content:
        '为了更好地服务客户，我们对月嫂服务标准进行了全面升级，包括新生儿护理、产妇护理等各个环节的标准化操作流程。',
      coverImage: 'https://picsum.photos/seed/news1/400/300',
      author: '运营部',
      publishTime: '2024-06-25 10:00',
      status: 'published',
      views: 1250,
      category: '服务标准'
    },
    {
      id: '2',
      title: '家政服务行业发展趋势分析',
      content:
        '随着人们生活水平的提高，家政服务需求日益增长。本文分析了当前家政服务行业的发展趋势和未来发展方向。',
      coverImage: 'https://picsum.photos/seed/news2/400/300',
      author: '市场部',
      publishTime: '2024-06-24 14:30',
      status: 'published',
      views: 890,
      category: '行业动态'
    },
    {
      id: '3',
      title: '如何选择合适的家政服务',
      content:
        '面对市场上众多的家政服务选择，如何找到最适合自己的服务？本文为您提供详细的选购指南。',
      coverImage: 'https://picsum.photos/seed/news3/400/300',
      author: '客服部',
      publishTime: '2024-06-23 16:20',
      status: 'draft',
      views: 0,
      category: '选购指南'
    }
  ],
  total: 3
}

// 服务套餐 Mock 数据
export const mockServicePackageData = {
  list: [
    {
      id: 'SP001',
      name: '日常保洁-3小时',
      category: '日常保洁',
      price: 150.0,
      originalPrice: 180.0,
      unit: '次',
      packageType: 'count-card',
      taskSplitRule: '单次3小时上门服务',
      status: 'active',
      createTime: '2024-06-20 10:00',
      updateTime: '2024-06-20 10:00',
      thumbnail: 'https://picsum.photos/seed/p1/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c1/800/400',
        'https://picsum.photos/seed/c2/800/400'
      ],
      features: ['24小时服务', '专业认证', '上门服务'],
      serviceDescription: '专业日常保洁服务，涵盖卧室、客厅、厨房、卫生间等各个区域的清洁工作。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 卧室：床铺整理、地面吸尘/擦拭、桌面整理</li><li>✅ 客厅：地面清洁、沙发整理、茶几擦拭</li><li>✅ 厨房：台面清洁、油烟机表面擦拭、地面清洁</li><li>✅ 卫生间：马桶/台盆清洁、镜面擦拭、地面清洁</li></ul><p><strong>【服务标准】</strong></p><ul><li>✨ 做到表面无尘、无水渍、无毛发</li><li>✨ 物品归位整齐，焕然一新</li></ul>`,
      serviceProcess: `<p>1. <strong>服务前沟通</strong>：与您确认服务范围及重点区域。</p><p>2. <strong>工具准备</strong>：自备专业环保清洁工具。</p><p>3. <strong>分区清洁</strong>：按厨房、卧室、客厅、卫生间顺序进行。</p><p>4. <strong>服务后验收</strong>：与您确认清洁效果，确保满意。</p>`,
      purchaseNotice: `<p>1. 请提前将贵重物品、私人物品收纳好。</p><p>2. 为了您的宠物安全，请在服务期间将其安置在固定区域。</p><p>3. 如有特殊清洁需求或禁忌，请务必提前告知服务人员。</p>`,
      bookingConfig: {
        advanceBookingDays: 1,
        maxBookingDays: 30,
        cancellationPolicy: '提前24小时取消，不收取费用'
      }
    },
    {
      id: 'SP002',
      name: '油烟机深度清洗',
      category: '深度保洁',
      price: 188.0,
      originalPrice: 220.0,
      unit: '次',
      packageType: 'count-card',
      taskSplitRule: '单次2小时专项服务',
      status: 'active',
      createTime: '2024-06-18 14:30',
      updateTime: '2024-06-18 14:30',
      thumbnail: 'https://picsum.photos/seed/p2/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c3/800/400',
        'https://picsum.photos/seed/c4/800/400'
      ],
      features: ['专业设备', '深度清洁', '品质保障'],
      serviceDescription: '专业油烟机深度清洗服务，使用专业设备彻底清洁油烟机内外，去除顽固油污。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 油烟机机体、油网、油盒、涡轮的全面拆洗</li><li>✅ 专业级化油剂浸泡，高效去油污</li></ul>`,
      serviceProcess: `<p>1. <strong>环境勘察</strong>：检查油烟机型号及工作状态。</p><p>2. <strong>周边保护</strong>：使用专业保护膜保护灶台及墙面。</p><p>3. <strong>拆卸清洗</strong>：精细拆卸各部件，进行深度清洗。</p><p>4. <strong>安装测试</strong>：重新组装并开机测试，确保功能正常。</p>`,
      purchaseNotice: `<p>1. 本服务仅针对家用油烟机。</p><p>2. 对于老旧或特殊型号的油烟机，服务人员会现场评估是否可以拆洗。</p>`,
      bookingConfig: {
        advanceBookingDays: 3,
        maxBookingDays: 60,
        cancellationPolicy: '提前48小时取消，不收取费用'
      }
    },
    {
      id: 'SP003',
      name: '金牌月嫂-26天贴心陪护',
      category: '月嫂服务',
      price: 8800.0,
      originalPrice: 9800.0,
      unit: '项',
      packageType: 'long-term',
      taskSplitRule: '26天每日24小时服务',
      status: 'active',
      createTime: '2024-06-15 09:00',
      updateTime: '2024-06-15 09:00',
      thumbnail: 'https://picsum.photos/seed/p3/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c5/800/400',
        'https://picsum.photos/seed/c6/800/400'
      ],
      features: ['专业月嫂', '产后护理', '24小时服务'],
      serviceDescription:
        '专业月嫂26天贴心陪护服务，涵盖新生儿护理、产妇护理、月子餐制作等全方位服务。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 新生儿护理：喂养、洗澡、抚触、早教启蒙</li><li>✅ 产妇护理：月子餐制作、产后恢复指导</li><li>✅ 家务协助：婴儿用品清洗、房间整理</li></ul>`,
      serviceProcess: `<p>1. <strong>入户评估</strong>：了解产妇和新生儿情况。</p><p>2. <strong>制定计划</strong>：个性化护理方案。</p><p>3. <strong>专业护理</strong>：24小时贴心照护。</p><p>4. <strong>指导培训</strong>：传授育儿知识和技巧。</p>`,
      purchaseNotice: `<p>1. 服务期间请提供独立房间。</p><p>2. 如有特殊饮食要求请提前告知。</p>`,
      bookingConfig: {
        advanceBookingDays: 7,
        maxBookingDays: 90,
        cancellationPolicy: '提前7天取消，不收取费用'
      }
    },
    // 待上架数据
    {
      id: 'SP004',
      name: '冰箱除菌清洁',
      category: '家电清洗',
      price: 99.0,
      originalPrice: 120.0,
      unit: '次',
      packageType: 'count-card',
      taskSplitRule: '单次1.5小时专项服务',
      status: 'pending',
      createTime: '2024-06-25 11:00',
      updateTime: '2024-06-25 11:00',
      thumbnail: 'https://picsum.photos/seed/p4/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c7/800/400',
        'https://picsum.photos/seed/c8/800/400'
      ],
      features: ['专业设备', '除菌清洁', '品质保障'],
      serviceDescription: '专业冰箱除菌清洁服务，使用专业设备彻底清洁冰箱内外，去除细菌和异味。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 冰箱内部全面清洁消毒</li><li>✅ 除味除菌处理</li><li>✅ 密封条清洁保养</li></ul>`,
      serviceProcess: `<p>1. <strong>设备检查</strong>：检查冰箱工作状态。</p><p>2. <strong>清洁消毒</strong>：使用专业清洁剂进行清洁。</p><p>3. <strong>除味处理</strong>：去除异味和细菌。</p><p>4. <strong>保养维护</strong>：密封条保养和功能测试。</p>`,
      purchaseNotice: `<p>1. 请提前清空冰箱内容物。</p><p>2. 服务期间请保持冰箱断电。</p>`,
      bookingConfig: {
        advanceBookingDays: 2,
        maxBookingDays: 30,
        cancellationPolicy: '提前24小时取消，不收取费用'
      }
    },
    {
      id: 'SP005',
      name: '4次收纳整理套餐',
      category: '收纳整理',
      price: 1200.0,
      originalPrice: 1500.0,
      unit: '项',
      packageType: 'count-card',
      taskSplitRule: '4次上门服务,每次4小时',
      status: 'pending',
      createTime: '2024-06-22 16:20',
      updateTime: '2024-06-22 16:20',
      thumbnail: 'https://picsum.photos/seed/p5/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c9/800/400',
        'https://picsum.photos/seed/c10/800/400'
      ],
      features: ['专业收纳', '4次服务', '上门整理'],
      serviceDescription: '专业收纳整理服务，4次上门服务，每次4小时，帮您打造整洁有序的生活空间。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 衣物收纳整理</li><li>✅ 书籍文件整理</li><li>✅ 厨房用品整理</li><li>✅ 儿童玩具整理</li></ul>`,
      serviceProcess: `<p>1. <strong>空间评估</strong>：了解收纳需求和空间布局。</p><p>2. <strong>制定方案</strong>：个性化收纳方案。</p><p>3. <strong>分类整理</strong>：按类别进行整理收纳。</p><p>4. <strong>标签管理</strong>：建立标签系统便于查找。</p>`,
      purchaseNotice: `<p>1. 请提前准备收纳用品。</p><p>2. 服务期间请配合整理工作。</p>`,
      bookingConfig: {
        advanceBookingDays: 3,
        maxBookingDays: 60,
        cancellationPolicy: '提前48小时取消，不收取费用'
      }
    },
    // 回收站数据
    {
      id: 'SP006',
      name: '空调深度清洗',
      category: '家电清洗',
      price: 200.0,
      originalPrice: 250.0,
      unit: '次',
      packageType: 'count-card',
      taskSplitRule: '单次2小时专项服务',
      status: 'deleted',
      createTime: '2024-06-10 09:30',
      updateTime: '2024-06-10 09:30',
      thumbnail: 'https://picsum.photos/seed/p6/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c11/800/400',
        'https://picsum.photos/seed/c12/800/400'
      ],
      features: ['专业设备', '深度清洗', '品质保障'],
      serviceDescription: '专业空调深度清洗服务，使用专业设备彻底清洁空调内外，去除灰尘和细菌。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 空调滤网清洗</li><li>✅ 蒸发器清洗</li><li>✅ 冷凝器清洗</li><li>✅ 风道清洁</li></ul>`,
      serviceProcess: `<p>1. <strong>设备检查</strong>：检查空调工作状态。</p><p>2. <strong>拆卸清洗</strong>：拆卸各部件进行清洗。</p><p>3. <strong>深度清洁</strong>：使用专业设备深度清洁。</p><p>4. <strong>安装测试</strong>：重新安装并测试功能。</p>`,
      purchaseNotice: `<p>1. 请提前关闭空调电源。</p><p>2. 服务期间请保持室内通风。</p>`,
      bookingConfig: {
        advanceBookingDays: 2,
        maxBookingDays: 30,
        cancellationPolicy: '提前24小时取消，不收取费用'
      }
    },
    {
      id: 'SP007',
      name: '地板打蜡护理',
      category: '专项服务',
      price: 300.0,
      originalPrice: 380.0,
      unit: '次',
      packageType: 'count-card',
      taskSplitRule: '单次3小时专项服务',
      status: 'deleted',
      createTime: '2024-06-05 14:15',
      updateTime: '2024-06-05 14:15',
      thumbnail: 'https://picsum.photos/seed/p7/400/400',
      carouselImages: [
        'https://picsum.photos/seed/c13/800/400',
        'https://picsum.photos/seed/c14/800/400'
      ],
      features: ['专业设备', '打蜡护理', '品质保障'],
      serviceDescription: '专业地板打蜡护理服务，使用专业设备和材料，让地板焕发光彩。',
      serviceDetails: `<p><strong>【服务范围】</strong></p><ul><li>✅ 地板清洁打磨</li><li>✅ 专业打蜡处理</li><li>✅ 抛光护理</li><li>✅ 保养维护</li></ul>`,
      serviceProcess: `<p>1. <strong>地面检查</strong>：检查地板材质和状况。</p><p>2. <strong>清洁打磨</strong>：彻底清洁和打磨地板。</p><p>3. <strong>打蜡处理</strong>：使用专业蜡进行打蜡。</p><p>4. <strong>抛光护理</strong>：抛光处理让地板光亮。</p>`,
      purchaseNotice: `<p>1. 请提前清空地面物品。</p><p>2. 服务期间请避免踩踏。</p>`,
      bookingConfig: {
        advanceBookingDays: 3,
        maxBookingDays: 30,
        cancellationPolicy: '提前48小时取消，不收取费用'
      }
    }
  ],
  total: 7
}

// 分页参数接口
export interface PaginationParams {
  page: number
  size: number
}

// 筛选参数接口
export interface FilterParams {
  keyword?: string
  status?: string
  category?: string
  dateRange?: [string, string]
}

// 响应数据接口
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

export interface ListResponse<T> {
  list: T[]
  total: number
  page: number
  size: number
}
