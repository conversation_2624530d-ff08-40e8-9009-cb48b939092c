# 资源中心-证书模板接口文档

## 接口概述

### 基础信息

- **基础路径**: `/publicbiz/certificate/template`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求头**: `Content-Type: application/json`

### 统一响应格式

```json
{
  "code": 0, // 状态码，0表示成功，非0表示失败
  "msg": "success", // 响应消息
  "data": {} // 响应数据，具体结构见各接口说明
}
```

## 1. 证书模板分页查询

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/page`
- **请求方式**: GET
- **功能说明**: 分页查询证书模板列表，支持按类型、状态、关键词筛选

### 请求参数

| 参数名  | 类型    | 必填 | 说明                                                             |
| ------- | ------- | ---- | ---------------------------------------------------------------- |
| page    | Integer | 否   | 页码，默认1                                                      |
| size    | Integer | 否   | 每页大小，默认10                                                 |
| type    | String  | 否   | 证书类型：training-培训证书，completion-结业证书，skill-技能证书 |
| status  | String  | 否   | 状态：draft-草稿，active-启用中，inactive-已停用                 |
| keyword | String  | 否   | 搜索关键词，模糊匹配模板名称                                     |

### 响应字段

| 字段名              | 类型    | 说明          |
| ------------------- | ------- | ------------- |
| total               | Integer | 总记录数      |
| list                | Array   | 证书模板列表  |
| list[].id           | Long    | 模板ID        |
| list[].name         | String  | 模板名称      |
| list[].type         | String  | 证书类型      |
| list[].description  | String  | 模板描述      |
| list[].course       | String  | 适用课程ID    |
| list[].course_name  | String  | 适用课程名称  |
| list[].status       | String  | 状态          |
| list[].html_content | String  | HTML模板内容  |
| list[].preview_url  | String  | 模板预览图URL |
| list[].creator      | String  | 创建人        |
| list[].creator_name | String  | 创建人姓名    |
| list[].create_time  | String  | 创建时间      |
| list[].updater      | String  | 更新人        |
| list[].update_time  | String  | 更新时间      |

### 请求示例

```
GET /publicbiz/certificate/template/page?page=1&size=10&type=training&status=active&keyword=新媒体
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "name": "新媒体初级培训证书模板",
        "type": "training",
        "description": "新媒体初级培训证书模板描述",
        "course": "COURSE_001",
        "course_name": "新媒体初级培训",
        "status": "active",
        "html_content": "<div class=\"certificate-template\">...</div>",
        "preview_url": "https://example.com/preview/1.jpg",
        "creator": "admin",
        "creator_name": "管理员",
        "create_time": "2024-08-01 10:00:00",
        "updater": "admin",
        "update_time": "2024-08-01 10:00:00"
      }
    ]
  }
}
```

## 2. 证书模板列表查询

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/list`
- **请求方式**: GET
- **功能说明**: 获取证书模板简单列表，不分页，用于下拉选择等场景

### 请求参数

| 参数名 | 类型   | 必填 | 说明                       |
| ------ | ------ | ---- | -------------------------- |
| status | String | 否   | 状态筛选，默认返回所有状态 |

### 响应字段

| 字段名 | 类型   | 说明     |
| ------ | ------ | -------- |
| id     | Long   | 模板ID   |
| name   | String | 模板名称 |
| type   | String | 证书类型 |
| status | String | 状态     |

### 请求示例

```
GET /publicbiz/certificate/template/list?status=active
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "name": "新媒体初级培训证书模板",
      "type": "training",
      "status": "active"
    }
  ]
}
```

## 3. 新增证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/create`
- **请求方式**: POST
- **功能说明**: 创建新的证书模板

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| name | String | 是 | 模板名称，最大长度100字符 |
| type | String | 是 | 证书类型：training-培训证书，completion-结业证书，skill-技能证书 |
| description | String | 否 | 模板描述 |
| course | String | 否 | 适用课程ID |
| course_name | String | 否 | 适用课程名称 |
| status | String | 是 | 状态：draft-草稿，active-启用中，inactive-已停用 |
| html_content | String | 否 | HTML模板内容，支持占位符变量 |
| background_url | String | 否 | 背景图片URL |
| fields | Array | 否 | 证书字段配置数组 |
| fields[].id | String | 是 | 字段ID |
| fields[].type | String | 是 | 字段类型：name-学员姓名，code-证书编号，id-身份证，date-发证日期 |
| fields[].label | String | 是 | 字段标签 |
| fields[].x | Integer | 是 | 字段X坐标 |
| fields[].y | Integer | 是 | 字段Y坐标 |
| fields[].font_size | Integer | 是 | 字体大小 |
| fields[].color | String | 是 | 字体颜色 |
| fields[].font_family | String | 是 | 字体族 |

### 响应字段

| 字段名 | 类型 | 说明           |
| ------ | ---- | -------------- |
| id     | Long | 新创建的模板ID |

### 请求示例

```json
POST /publicbiz/certificate/template/create
Content-Type: application/json

{
  "name": "新媒体初级培训证书模板",
  "type": "training",
  "description": "新媒体初级培训证书模板描述",
  "course": "COURSE_001",
  "course_name": "新媒体初级培训",
  "status": "active",
  "background_url": "https://example.com/bg.jpg",
  "fields": [
    {
      "id": "field_1",
      "type": "name",
      "label": "【学员姓名】",
      "x": 350,
      "y": 200,
      "font_size": 32,
      "color": "#333",
      "font_family": "微软雅黑"
    },
    {
      "id": "field_2",
      "type": "date",
      "label": "【发证日期】",
      "x": 350,
      "y": 400,
      "font_size": 16,
      "color": "#666",
      "font_family": "微软雅黑"
    }
  ]
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1
  }
}
```

## 4. 编辑证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/update`
- **请求方式**: POST
- **功能说明**: 更新现有证书模板

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| id | Long | 是 | 模板ID |
| name | String | 是 | 模板名称，最大长度100字符 |
| type | String | 是 | 证书类型：training-培训证书，completion-结业证书，skill-技能证书 |
| description | String | 否 | 模板描述 |
| course | String | 否 | 适用课程ID |
| course_name | String | 否 | 适用课程名称 |
| status | String | 是 | 状态：draft-草稿，active-启用中，inactive-已停用 |
| html_content | String | 否 | HTML模板内容，支持占位符变量 |
| background_url | String | 否 | 背景图片URL |
| fields | Array | 否 | 证书字段配置数组，结构同新增接口 |

### 响应字段

无特殊响应数据，仅返回基础响应格式

### 请求示例

```json
POST /publicbiz/certificate/template/update
Content-Type: application/json

{
  "id": 1,
  "name": "新媒体初级培训证书模板（更新版）",
  "type": "training",
  "description": "更新后的模板描述",
  "course": "COURSE_001",
  "course_name": "新媒体初级培训",
  "status": "active",
  "background_url": "https://example.com/bg-new.jpg",
  "fields": [
    {
      "id": "field_1",
      "type": "name",
      "label": "【学员姓名】",
      "x": 350,
      "y": 200,
      "font_size": 36,
      "color": "#000",
      "font_family": "微软雅黑"
    }
  ]
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 5. 证书模板详情查询

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/detail`
- **请求方式**: GET
- **功能说明**: 根据ID获取证书模板详细信息

### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

### 响应字段

| 字段名               | 类型    | 说明             |
| -------------------- | ------- | ---------------- |
| id                   | Long    | 模板ID           |
| name                 | String  | 模板名称         |
| type                 | String  | 证书类型         |
| description          | String  | 模板描述         |
| course               | String  | 适用课程ID       |
| course_name          | String  | 适用课程名称     |
| status               | String  | 状态             |
| html_content         | String  | HTML模板内容     |
| preview_url          | String  | 模板预览图URL    |
| fields               | Array   | 证书字段配置数组 |
| fields[].id          | String  | 字段ID           |
| fields[].type        | String  | 字段类型         |
| fields[].label       | String  | 字段标签         |
| fields[].x           | Integer | 字段X坐标        |
| fields[].y           | Integer | 字段Y坐标        |
| fields[].font_size   | Integer | 字体大小         |
| fields[].color       | String  | 字体颜色         |
| fields[].font_family | String  | 字体族           |
| creator              | String  | 创建人           |
| creator_name         | String  | 创建人姓名       |
| create_time          | String  | 创建时间         |
| updater              | String  | 更新人           |
| update_time          | String  | 更新时间         |

### 请求示例

```
GET /publicbiz/certificate/template/detail?id=1
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "新媒体初级培训证书模板",
    "type": "training",
    "description": "新媒体初级培训证书模板描述",
    "course": "COURSE_001",
    "course_name": "新媒体初级培训",
    "status": "active",
    "html_content": "<div class=\"certificate-template\">...</div>",
    "preview_url": "https://example.com/preview/1.jpg",
    "fields": [
      {
        "id": "field_1",
        "type": "name",
        "label": "【学员姓名】",
        "x": 350,
        "y": 200,
        "font_size": 32,
        "color": "#333",
        "font_family": "微软雅黑"
      }
    ],
    "creator": "admin",
    "creator_name": "管理员",
    "create_time": "2024-08-01 10:00:00",
    "updater": "admin",
    "update_time": "2024-08-01 10:00:00"
  }
}
```

## 6. 删除证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/delete`
- **请求方式**: POST
- **功能说明**: 删除指定的证书模板（逻辑删除）

### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

### 响应字段

无特殊响应数据，仅返回基础响应格式

### 请求示例

```json
POST /publicbiz/certificate/template/delete
Content-Type: application/json

{
  "id": 1
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 7. 启用证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/enable`
- **请求方式**: POST
- **功能说明**: 启用指定的证书模板

### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

### 响应字段

无特殊响应数据，仅返回基础响应格式

### 请求示例

```json
POST /publicbiz/certificate/template/enable
Content-Type: application/json

{
  "id": 1
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 8. 停用证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/disable`
- **请求方式**: POST
- **功能说明**: 停用指定的证书模板

### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

### 响应字段

无特殊响应数据，仅返回基础响应格式

### 请求示例

```json
POST /publicbiz/certificate/template/disable
Content-Type: application/json

{
  "id": 1
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 9. 证书模板预览

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/preview`
- **请求方式**: GET
- **功能说明**: 获取证书模板预览图片URL

### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

### 响应字段

| 字段名      | 类型   | 说明        |
| ----------- | ------ | ----------- |
| preview_url | String | 预览图片URL |

### 请求示例

```
GET /publicbiz/certificate/template/preview?id=1
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "preview_url": "https://example.com/preview/1.jpg"
  }
}
```

## 10. 证书模板统计报表

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/statistics`
- **请求方式**: GET
- **功能说明**: 获取证书模板统计数据，包括总数、启用数、停用数、草稿数

### 请求参数

无

### 响应字段

| 字段名         | 类型    | 说明         |
| -------------- | ------- | ------------ |
| total_count    | Integer | 证书模板总数 |
| active_count   | Integer | 启用中数量   |
| inactive_count | Integer | 已停用数量   |
| draft_count    | Integer | 草稿数量     |

### 请求示例

```
GET /publicbiz/certificate/template/statistics
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total_count": 25,
    "active_count": 18,
    "inactive_count": 5,
    "draft_count": 2
  }
}
```

## 11. 批量操作证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/batch-operation`
- **请求方式**: POST
- **功能说明**: 批量启用、停用或删除证书模板

### 请求参数

| 参数名    | 类型   | 必填 | 说明                                             |
| --------- | ------ | ---- | ------------------------------------------------ |
| ids       | Array  | 是   | 模板ID数组                                       |
| operation | String | 是   | 操作类型：enable-启用，disable-停用，delete-删除 |

### 响应字段

| 字段名        | 类型    | 说明                 |
| ------------- | ------- | -------------------- |
| success_count | Integer | 成功操作的数量       |
| failed_count  | Integer | 失败操作的数量       |
| failed_ids    | Array   | 操作失败的模板ID列表 |

### 请求示例

```json
POST /publicbiz/certificate/template/batch-operation
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "operation": "enable"
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "success_count": 2,
    "failed_count": 1,
    "failed_ids": [3]
  }
}
```

## 12. 复制证书模板

### 接口信息

- **接口地址**: `/publicbiz/certificate/template/copy`
- **请求方式**: POST
- **功能说明**: 复制现有证书模板创建新模板

### 请求参数

| 参数名    | 类型   | 必填 | 说明       |
| --------- | ------ | ---- | ---------- |
| source_id | Long   | 是   | 源模板ID   |
| name      | String | 是   | 新模板名称 |

### 响应字段

| 字段名 | 类型 | 说明           |
| ------ | ---- | -------------- |
| id     | Long | 新创建的模板ID |

### 请求示例

```json
POST /publicbiz/certificate/template/copy
Content-Type: application/json

{
  "source_id": 1,
  "name": "新媒体初级培训证书模板（副本）"
}
```

### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 26
  }
}
```

## 数据字典

### 证书类型枚举 (type)

| 值         | 说明     |
| ---------- | -------- |
| training   | 培训证书 |
| completion | 结业证书 |
| skill      | 技能证书 |

### 模板状态枚举 (status)

| 值       | 说明   |
| -------- | ------ |
| draft    | 草稿   |
| active   | 启用中 |
| inactive | 已停用 |

### 字段类型枚举 (fields[].type)

| 值   | 说明     |
| ---- | -------- |
| name | 学员姓名 |
| code | 证书编号 |
| id   | 身份证号 |
| date | 发证日期 |

### 批量操作类型枚举 (operation)

| 值      | 说明 |
| ------- | ---- |
| enable  | 启用 |
| disable | 停用 |
| delete  | 删除 |

## 错误码说明

### 通用错误码

| 错误码 | 说明           |
| ------ | -------------- |
| 0      | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权访问     |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

### 业务错误码

| 错误码 | 说明                         |
| ------ | ---------------------------- |
| 10001  | 证书模板名称已存在           |
| 10002  | 证书模板不存在               |
| 10003  | 证书模板正在使用中，无法删除 |
| 10004  | HTML模板内容格式错误         |
| 10005  | 证书字段配置错误             |
| 10006  | 背景图片格式不支持           |
| 10007  | 模板状态不允许此操作         |
| 10008  | 批量操作部分失败             |

### 错误响应示例

```json
{
  "code": 10001,
  "msg": "证书模板名称已存在",
  "data": null
}
```

## 接口使用说明

### 1. 认证方式

所有接口都需要在请求头中携带认证信息：

```
Authorization: Bearer {access_token}
```

### 2. 分页参数说明

- `page`: 页码，从1开始
- `size`: 每页大小，建议范围1-100，默认10

### 3. 时间格式

所有时间字段统一使用格式：`yyyy-MM-dd HH:mm:ss`

### 4. HTML模板占位符

HTML模板内容支持以下占位符变量：

- `{{studentName}}`: 学员姓名
- `{{certificateCode}}`: 证书编号
- `{{issueDate}}`: 发证日期
- `{{idNumber}}`: 身份证号

### 5. 字段坐标说明

- 坐标系以证书模板左上角为原点(0,0)
- X轴向右为正方向，Y轴向下为正方向
- 单位为像素(px)
- 建议证书模板尺寸：900px × 600px

### 6. 图片上传说明

- 支持格式：JPG、PNG、GIF
- 文件大小限制：5MB以内
- 建议尺寸：900px × 600px
- 图片会自动压缩和优化

## 版本历史

| 版本 | 日期       | 说明                       |
| ---- | ---------- | -------------------------- |
| v1.0 | 2024-08-07 | 初始版本，包含基础CRUD操作 |
| v1.1 | 2024-08-07 | 新增批量操作和复制功能     |
| v1.2 | 2024-08-07 | 新增统计报表接口           |
