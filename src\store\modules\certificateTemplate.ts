import { defineStore } from 'pinia'
import { store } from '@/store'
import {
  CertificateTemplateVO,
  CertificateField,
  CreateCertificateTemplateParams,
  UpdateCertificateTemplateParams,
  CertificateTemplateApi,
  mockCertificateTemplates
} from '@/api/infra/certificateTemplate'
import { ElMessage } from 'element-plus'
import { generateCertificateHtml, validateHtmlTemplate } from '@/utils/htmlSanitizer'

interface CertificateTemplateState {
  // 当前编辑的模板
  currentTemplate: CertificateTemplateVO | null
  // 模板列表
  templateList: CertificateTemplateVO[]
  // 最近保存的模板ID
  lastSavedTemplateId: number | null
  // 加载状态
  loading: boolean
}

export const useCertificateTemplateStore = defineStore('certificateTemplate', {
  state: (): CertificateTemplateState => ({
    currentTemplate: null,
    templateList: [...mockCertificateTemplates], // 初始化mock数据
    lastSavedTemplateId: null,
    loading: false
  }),

  getters: {
    // 获取当前模板
    getCurrentTemplate(): CertificateTemplateVO | null {
      return this.currentTemplate
    },

    // 根据ID获取模板
    getTemplateById:
      (state) =>
      (id: number): CertificateTemplateVO | null => {
        return state.templateList.find((template) => template.id === id) || null
      },

    // 获取最近保存的模板
    getLastSavedTemplate(): CertificateTemplateVO | null {
      if (!this.lastSavedTemplateId) return null
      return this.getTemplateById(this.lastSavedTemplateId)
    },

    // 获取模板列表
    getTemplateList(): CertificateTemplateVO[] {
      return this.templateList
    }
  },

  actions: {
    // 设置当前编辑的模板
    setCurrentTemplate(template: CertificateTemplateVO | null) {
      this.currentTemplate = template
    },

    // 创建新模板
    async createTemplate(templateData: CreateCertificateTemplateParams): Promise<number | null> {
      this.loading = true
      try {
        // 生成HTML模板内容
        let htmlContent = templateData.htmlContent
        if (!htmlContent && templateData.fields && templateData.fields.length > 0) {
          htmlContent = generateCertificateHtml(templateData.fields, templateData.backgroundUrl)
        }

        // 验证HTML模板安全性
        if (htmlContent && !validateHtmlTemplate(htmlContent)) {
          ElMessage.error('HTML模板包含不安全的内容，请检查后重试')
          return null
        }

        // 在实际项目中，这里会调用真实的API
        // const result = await CertificateTemplateApi.create(templateData)

        // 使用mock数据模拟API调用
        const newId = Math.max(...this.templateList.map((t) => t.id || 0)) + 1
        const newTemplate: CertificateTemplateVO = {
          ...templateData,
          htmlContent,
          id: newId,
          creator: 'admin',
          createTime: new Date().toLocaleString(),
          updater: 'admin',
          updateTime: new Date().toLocaleString()
        }

        // 添加到列表
        this.templateList.push(newTemplate)

        // 设置为当前模板
        this.currentTemplate = newTemplate
        this.lastSavedTemplateId = newId

        ElMessage.success('证书模板保存成功')
        return newId
      } catch (error) {
        console.error('创建证书模板失败:', error)
        ElMessage.error('保存失败，请重试')
        return null
      } finally {
        this.loading = false
      }
    },

    // 更新模板
    async updateTemplate(templateData: UpdateCertificateTemplateParams): Promise<boolean> {
      this.loading = true
      try {
        // 生成HTML模板内容
        let htmlContent = templateData.htmlContent
        if (!htmlContent && templateData.fields && templateData.fields.length > 0) {
          htmlContent = generateCertificateHtml(templateData.fields, templateData.backgroundUrl)
        }

        // 验证HTML模板安全性
        if (htmlContent && !validateHtmlTemplate(htmlContent)) {
          ElMessage.error('HTML模板包含不安全的内容，请检查后重试')
          return false
        }

        // 在实际项目中，这里会调用真实的API
        // await CertificateTemplateApi.update(templateData)

        // 使用mock数据模拟API调用
        const index = this.templateList.findIndex((t) => t.id === templateData.id)
        if (index !== -1) {
          this.templateList[index] = {
            ...templateData,
            htmlContent,
            updater: 'admin',
            updateTime: new Date().toLocaleString()
          }

          // 更新当前模板
          if (this.currentTemplate?.id === templateData.id) {
            this.currentTemplate = this.templateList[index]
          }

          this.lastSavedTemplateId = templateData.id
        }

        ElMessage.success('证书模板更新成功')
        return true
      } catch (error) {
        console.error('更新证书模板失败:', error)
        ElMessage.error('更新失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },

    // 删除模板
    async deleteTemplate(id: number): Promise<boolean> {
      this.loading = true
      try {
        // 在实际项目中，这里会调用真实的API
        // await CertificateTemplateApi.delete(id)

        // 使用mock数据模拟API调用
        const index = this.templateList.findIndex((t) => t.id === id)
        if (index !== -1) {
          this.templateList.splice(index, 1)

          // 如果删除的是当前模板，清空当前模板
          if (this.currentTemplate?.id === id) {
            this.currentTemplate = null
          }

          // 如果删除的是最近保存的模板，清空记录
          if (this.lastSavedTemplateId === id) {
            this.lastSavedTemplateId = null
          }
        }

        ElMessage.success('证书模板删除成功')
        return true
      } catch (error) {
        console.error('删除证书模板失败:', error)
        ElMessage.error('删除失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },

    // 获取模板详情
    async getTemplateDetail(id: number): Promise<CertificateTemplateVO | null> {
      this.loading = true
      try {
        // 在实际项目中，这里会调用真实的API
        // const template = await CertificateTemplateApi.getDetail(id)

        // 使用mock数据模拟API调用
        const template = this.templateList.find((t) => t.id === id)
        if (template) {
          this.currentTemplate = template
          return template
        }
        return null
      } catch (error) {
        console.error('获取证书模板详情失败:', error)
        ElMessage.error('获取模板详情失败')
        return null
      } finally {
        this.loading = false
      }
    },

    // 清空当前模板
    clearCurrentTemplate() {
      this.currentTemplate = null
    },

    // 重置store
    resetStore() {
      this.currentTemplate = null
      this.templateList = [...mockCertificateTemplates]
      this.lastSavedTemplateId = null
      this.loading = false
    }
  },

  // 启用持久化
  persist: {
    key: 'certificate-template-store',
    storage: localStorage,
    paths: ['templateList', 'lastSavedTemplateId']
  }
})

// 在setup外使用
export const useCertificateTemplateStoreWithOut = () => {
  return useCertificateTemplateStore(store)
}
