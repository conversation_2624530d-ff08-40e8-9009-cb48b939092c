<template>
  <el-dialog
    v-model="visible"
    title="证书预览"
    width="1000px"
    top="40px"
    :close-on-click-modal="false"
    :show-close="true"
    class="preview-certificate-dialog"
  >
    <div class="preview-certificate-main">
      <!-- 使用HTML模板渲染 -->
      <div v-if="renderedHtml" class="certificate-html-content" v-html="renderedHtml"></div>

      <!-- 降级方案：如果没有HTML模板，使用原来的字段渲染方式 -->
      <div v-else class="certificate-card" :style="backgroundImageStyle">
        <div class="card-content">
          <!-- 动态渲染字段 -->
          <div
            v-for="field in previewData.fields"
            :key="field.id"
            class="preview-field"
            :style="{
              position: 'absolute',
              left: field.x + 'px',
              top: field.y + 'px',
              fontSize: field.fontSize + 'px',
              color: field.color,
              fontFamily: field.fontFamily
            }"
          >
            {{ getFieldDisplayValue(field) }}
          </div>

          <!-- 如果没有字段，显示默认内容 -->
          <template v-if="!previewData.fields || previewData.fields.length === 0">
            <div class="student-name">张三</div>
            <div class="certificate-title">培训证书</div>
            <div class="certificate-desc">兹证明学员已完成相关培训课程</div>
            <div class="issue-date"> <span>发证日期：</span><b>2024年8月1日</b> </div>
          </template>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useCertificateTemplateStore } from '@/store/modules/certificateTemplate'
import type { CertificateField, CertificateTemplateVO } from '@/api/infra/certificateTemplate'
import { replacePlaceholders } from '@/utils/htmlSanitizer'
import { defaultPlaceholderData } from '@/api/infra/certificateTemplate'

const props = defineProps<{ visible: boolean; data?: any }>()
const emit = defineEmits(['update:visible'])

// 使用证书模板store
const certificateTemplateStore = useCertificateTemplateStore()

const visible = ref(props.visible)
watch(
  () => props.visible,
  (v) => (visible.value = v)
)
watch(visible, (v) => emit('update:visible', v))

// 预览数据 - 优先使用最新保存的模板，其次使用传入的data
const previewData = computed((): CertificateTemplateVO => {
  // 如果有最新保存的模板，优先使用
  const lastSavedTemplate = certificateTemplateStore.getLastSavedTemplate
  if (lastSavedTemplate) {
    return lastSavedTemplate
  }

  // 否则使用传入的data
  if (props.data) {
    return props.data
  }

  // 默认数据
  return {
    name: '培训证书',
    type: '',
    description: '',
    background: '',
    course: '',
    status: '',
    fields: []
  }
})

// 背景图片样式
const backgroundImageStyle = computed(() => {
  const bgUrl = previewData.value.backgroundUrl
  return bgUrl
    ? {
        backgroundImage: `url(${bgUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    : {}
})

// 渲染后的HTML内容
const renderedHtml = computed(() => {
  const template = previewData.value
  if (!template.htmlContent) {
    return null
  }

  // 准备占位符数据（移除模板名称）
  const placeholderData = {
    ...defaultPlaceholderData
    // 不再包含 templateName
  }

  // 安全地替换占位符并返回HTML
  return replacePlaceholders(template.htmlContent, placeholderData)
})

// 获取字段显示值
const getFieldDisplayValue = (field: CertificateField): string => {
  const mockData: Record<string, string> = {
    name: '张三',
    code: 'CERT-2024-001',
    id: '123456789012345678',
    date: '2024年8月1日'
  }

  return mockData[field.type] || field.label
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.preview-certificate-dialog {
  .el-dialog__body {
    padding: 0 32px 24px 32px;
    background: #f8f9fa;
  }
}
.preview-certificate-main {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 480px;
}
.certificate-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px #e6e7e9;
  padding: 32px 32px 32px 32px;
  width: 520px;
  min-height: 380px;
  margin: 0 auto;
  position: relative;
}
.card-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 24px;
}
.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 260px;
  position: relative;
}
.student-name {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 24px;
  margin-top: 12px;
}
.certificate-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 8px;
}
.certificate-desc {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}
.issue-date {
  font-size: 16px;
  color: #222;
  margin-bottom: 12px;
  font-weight: bold;
}
.qrcode-field {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 15px;
  color: #333;
  border: 1px dashed #bbb;
  border-radius: 4px;
  padding: 4px 10px;
  background: #f8f9fa;
}

/* HTML模板内容样式 */
.certificate-html-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 480px;
  padding: 20px;
}

.certificate-html-content .certificate-template {
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

/* 确保HTML模板中的文本可选择 */
.certificate-html-content * {
  user-select: text;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .certificate-html-content .certificate-template {
    transform: scale(0.8);
    transform-origin: center;
  }
}
</style>
