# 数字资产接口文档

## 1. 新增数字资产
- **接口地址**：/api/publicbiz/digitalasset/create
- **请求方式**：POST
- **入参**：
```
{
  "name": "课程名称",
  "category": "课程分类",
  "status": "课程状态",
  "price": 199.00,
  "teacher": "讲师姓名",
  "merchant": "收款商户",
  "business_module": "所属业务板块",
  "location": "上课地点",
  "schedule": "排期",
  "total": 100,
  "enrolled": 20,
  "description": "课程详细介绍",
  "attachments": [
    {
      "name": "开课须知.md",
      "type": "文档",
      "file_url": "https://xxx.com/file1.md",
      "file_name": "开课须知.md"
    },
    {
      "name": "课程导论.mp4",
      "type": "视频",
      "file_url": "https://xxx.com/video1.mp4",
      "file_name": "课程导论.mp4"
    }
  ]
}
```
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": { "id": 1 }
}
```

## 2. 编辑数字资产
- **接口地址**：/api/publicbiz/digitalasset/update
- **请求方式**：POST
- **入参**：同新增，需包含id字段
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 3. 删除数字资产
- **接口地址**：/api/publicbiz/digitalasset/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 4. 数字资产详情
- **接口地址**：/api/publicbiz/digitalasset/detail
- **请求方式**：GET
- **入参**：`?id=1`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "课程A",
    "category": "家政技能",
    "status": "已上架",
    "price": 199.00,
    "teacher": "张三",
    "merchant": "汇成人力资源",
    "business_module": "家政服务",
    "location": "总部A座101教室",
    "schedule": "每周一、三、五 9:00-17:00",
    "total": 100,
    "enrolled": 20,
    "description": "课程详细介绍",
    "attachments": [
      {
        "id": 1,
        "name": "开课须知.md",
        "type": "文档",
        "file_url": "https://xxx.com/file1.md",
        "file_name": "开课须知.md"
      },
      {
        "id": 2,
        "name": "课程导论.mp4",
        "type": "视频",
        "file_url": "https://xxx.com/video1.mp4",
        "file_name": "课程导论.mp4"
      }
    ],
    "creator": "admin",
    "create_time": "2024-07-01 10:00:00",
    "updater": "admin",
    "update_time": "2024-07-01 10:00:00"
  }
}
```

## 5. 数字资产分页查询
- **接口地址**：/api/publicbiz/digitalasset/page
- **请求方式**：GET
- **入参**：`?page=1&size=10&category=家政技能&status=已上架&keyword=课程A`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "name": "课程A",
        "category": "家政技能",
        "status": "已上架",
        "price": 199.00,
        "teacher": "张三",
        "merchant": "汇成人力资源",
        "business_module": "家政服务",
        "location": "总部A座101教室",
        "schedule": "每周一、三、五 9:00-17:00",
        "total": 100,
        "enrolled": 20,
        "description": "课程详细介绍",
        "attachments": [
          {
            "id": 1,
            "name": "开课须知.md",
            "type": "文档",
            "file_url": "https://xxx.com/file1.md",
            "file_name": "开课须知.md"
          }
        ],
        "creator": "admin",
        "create_time": "2024-07-01 10:00:00",
        "updater": "admin",
        "update_time": "2024-07-01 10:00:00"
      }
    ]
  }
}
```

## 6. 数字资产统计卡片数据
- **接口地址**：/api/publicbiz/digitalasset/stat
- **请求方式**：GET
- **入参**：无
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "course": 60,
    "video": 20,
    "doc": 20
  }
}
```
- **请求示例**：
```
curl http://host/api/publicbiz/digitalasset/stat
```

## 7. 课程附件相关接口

### 7.1 新增课程附件
- **接口地址**：/api/publicbiz/digitalasset/attachment/create
- **请求方式**：POST
- **入参**：
```
{
  "asset_id": 1,
  "name": "课程导论.mp4",
  "type": "视频",
  "file_url": "https://xxx.com/video1.mp4",
  "file_name": "课程导论.mp4"
}
```
- **出参**：
```
{ "code": 0, "msg": "success", "data": { "id": 1 } }
```

### 7.2 删除课程附件
- **接口地址**：/api/publicbiz/digitalasset/attachment/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

### 7.3 查询课程附件列表
- **接口地址**：/api/publicbiz/digitalasset/attachment/list
- **请求方式**：GET
- **入参**：`?asset_id=1`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "asset_id": 1,
      "name": "开课须知.md",
      "type": "文档",
      "file_url": "https://xxx.com/file1.md",
      "file_name": "开课须知.md"
    },
    {
      "id": 2,
      "asset_id": 1,
      "name": "课程导论.mp4",
      "type": "视频",
      "file_url": "https://xxx.com/video1.mp4",
      "file_name": "课程导论.mp4"
    }
  ]
}
```
