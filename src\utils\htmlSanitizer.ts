import DOMPurify from 'dompurify'

// DOMPurify配置 - 只允许安全的HTML标签和属性
const ALLOWED_TAGS = [
  'div',
  'span',
  'p',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'strong',
  'b',
  'em',
  'i',
  'u',
  'br',
  'hr',
  'ul',
  'ol',
  'li',
  'table',
  'tr',
  'td',
  'th',
  'img',
  'a'
]

const ALLOWED_ATTR = [
  'style',
  'class',
  'id',
  'data-*',
  'src',
  'alt',
  'width',
  'height',
  'href',
  'target',
  'title'
]

// 配置DOMPurify
const purifyConfig = {
  ALLOWED_TAGS,
  ALLOWED_ATTR,
  ALLOW_DATA_ATTR: true,
  FORBID_SCRIPT: true,
  FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button'],
  FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
}

/**
 * 安全地清理HTML内容
 * @param html 原始HTML字符串
 * @returns 清理后的安全HTML字符串
 */
export function sanitizeHtml(html: string): string {
  if (!html || typeof html !== 'string') {
    return ''
  }

  return DOMPurify.sanitize(html, purifyConfig)
}

/**
 * 安全地替换HTML模板中的占位符
 * @param htmlTemplate HTML模板字符串
 * @param data 替换数据对象
 * @returns 替换后的安全HTML字符串
 */
export function replacePlaceholders(htmlTemplate: string, data: Record<string, string>): string {
  if (!htmlTemplate || typeof htmlTemplate !== 'string') {
    return ''
  }

  let result = htmlTemplate

  // 遍历数据对象，替换占位符
  Object.entries(data).forEach(([key, value]) => {
    // 对值进行HTML转义，防止XSS
    const escapedValue = escapeHtml(String(value || ''))
    // 使用正则表达式替换所有匹配的占位符
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
    result = result.replace(regex, escapedValue)
  })

  // 移除所有未匹配的占位符（包括 {{templateName}} 等）
  result = result.replace(/\{\{[^}]+\}\}/g, '')

  // 清理并返回安全的HTML
  return sanitizeHtml(result)
}

/**
 * HTML转义函数，防止XSS攻击
 * @param text 需要转义的文本
 * @returns 转义后的安全文本
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * 验证HTML模板的安全性
 * @param htmlTemplate HTML模板字符串
 * @returns 是否安全
 */
export function validateHtmlTemplate(htmlTemplate: string): boolean {
  if (!htmlTemplate || typeof htmlTemplate !== 'string') {
    return false
  }

  try {
    // 尝试清理HTML，如果出现异常则认为不安全
    const cleaned = sanitizeHtml(htmlTemplate)

    // 检查是否包含危险的脚本标签或事件处理器
    const dangerousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b/gi,
      /<object\b/gi,
      /<embed\b/gi
    ]

    return !dangerousPatterns.some((pattern) => pattern.test(cleaned))
  } catch (error) {
    console.error('HTML模板验证失败:', error)
    return false
  }
}

/**
 * 生成证书HTML模板
 * @param fields 字段配置数组
 * @param backgroundUrl 背景图片URL
 * @returns 生成的HTML模板字符串
 */
export function generateCertificateHtml(
  fields: Array<{
    id: string
    type: string
    label: string
    x: number
    y: number
    fontSize: number
    color: string
    fontFamily: string
  }>,
  backgroundUrl?: string
): string {
  const backgroundStyle = backgroundUrl
    ? `background-image: url(${backgroundUrl}); background-size: cover; background-position: center; background-repeat: no-repeat;`
    : 'background: #fcfcfd;'

  const fieldsHtml = fields
    .map((field) => {
      const placeholder = getPlaceholderByFieldType(field.type)
      return `
      <div style="
        position: absolute;
        left: ${field.x}px;
        top: ${field.y}px;
        font-size: ${field.fontSize}px;
        color: ${field.color};
        font-family: ${field.fontFamily};
        z-index: 10;
      ">
        {{${placeholder}}}
      </div>
    `
    })
    .join('')

  return `
    <div class="certificate-template" style="
      width: 900px;
      height: 600px;
      position: relative;
      ${backgroundStyle}
      border: 2px dashed #dcdfe6;
      border-radius: 12px;
      padding: 48px;
      box-sizing: border-box;
    ">
      ${fieldsHtml}
    </div>
  `
}

/**
 * 根据字段类型获取对应的占位符
 * @param fieldType 字段类型
 * @returns 占位符名称
 */
function getPlaceholderByFieldType(fieldType: string): string {
  const typeMap: Record<string, string> = {
    name: 'studentName',
    code: 'certificateCode',
    id: 'studentId',
    date: 'issueDate'
  }

  return typeMap[fieldType] || fieldType
}
