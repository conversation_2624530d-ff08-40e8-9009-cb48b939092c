-- 1. 考题主表（publicbiz_question）
CREATE TABLE `publicbiz_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `level1` varchar(64) NOT NULL DEFAULT '' COMMENT '一级分类',
  `level2` varchar(64) NOT NULL DEFAULT '' COMMENT '二级分类',
  `level3` varchar(64) NOT NULL DEFAULT '' COMMENT '三级分类',
  `cert_name` varchar(128) NOT NULL DEFAULT '' COMMENT '认定点名称',
  `title` varchar(512) NOT NULL DEFAULT '' COMMENT '题干',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '题型（单选/多选/判断/简答等）',
  `biz` varchar(64) NOT NULL DEFAULT '' COMMENT '业务模块',
  `answer` varchar(1024) NOT NULL DEFAULT '' COMMENT '参考答案',
  `creator` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考题主表';

-- 2. 考题分类表（publicbiz_question_category）
CREATE TABLE `publicbiz_question_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `level1` varchar(64) NOT NULL DEFAULT '' COMMENT '一级分类',
  `level2` varchar(64) NOT NULL DEFAULT '' COMMENT '二级分类',
  `level3` varchar(64) NOT NULL DEFAULT '' COMMENT '三级分类',
  `biz` varchar(64) NOT NULL DEFAULT '' COMMENT '业务模块',
  `creator` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考题分类表';

-- 3. 操作日志表（publicbiz_opt_log）
-- 详见通用操作日志表结构，无需重复创建。
