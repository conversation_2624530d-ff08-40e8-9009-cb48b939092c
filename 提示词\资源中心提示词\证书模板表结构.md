-- 1. 证书模板主表（publicbiz_certificate_template）CREATE TABLE `publicbiz_certificate_template` ( `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键', `name` varchar(128) NOT NULL DEFAULT '' COMMENT '模板名称', `type` varchar(64) NOT NULL DEFAULT '' COMMENT '模板类型', `background_url` varchar(512) NOT NULL DEFAULT '' COMMENT '证书背景图片URL', `content` text COMMENT '证书内容（JSON或富文本）', `preview_url` varchar(512) NOT NULL DEFAULT '' COMMENT '证书预览图URL', `status` varchar(32) NOT NULL DEFAULT '' COMMENT '状态（启用/停用/草稿）', `creator` varchar(64) DEFAULT '' COMMENT '创建者', `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` varchar(64) DEFAULT '' COMMENT '更新者', `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证书模板主表';
