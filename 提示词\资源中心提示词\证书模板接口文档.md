# 证书模板接口文档

## 1. 新增证书模板
- **接口地址**：/api/publicbiz/certificate/template/create
- **请求方式**：POST
- **入参**：
```
{
  "name": "模板名称",
  "type": "模板类型",
  "background_url": "https://xxx.com/bg.jpg",
  "content": "{...}",
  "preview_url": "https://xxx.com/preview.jpg",
  "status": "启用"
}
```
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": { "id": 1 }
}
```

## 2. 编辑证书模板
- **接口地址**：/api/publicbiz/certificate/template/update
- **请求方式**：POST
- **入参**：同新增，需包含id字段
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 3. 删除证书模板
- **接口地址**：/api/publicbiz/certificate/template/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 4. 证书模板详情
- **接口地址**：/api/publicbiz/certificate/template/detail
- **请求方式**：GET
- **入参**：`?id=1`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "模板A",
    "type": "荣誉证书",
    "background_url": "https://xxx.com/bg.jpg",
    "content": "{...}",
    "preview_url": "https://xxx.com/preview.jpg",
    "status": "启用",
    "creator": "admin",
    "create_time": "2024-07-01 10:00:00",
    "updater": "admin",
    "update_time": "2024-07-01 10:00:00"
  }
}
```

## 5. 证书模板分页查询
- **接口地址**：/api/publicbiz/certificate/template/page
- **请求方式**：GET
- **入参**：`?page=1&size=10&type=荣誉证书&status=启用&keyword=模板A`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "name": "模板A",
        "type": "荣誉证书",
        "background_url": "https://xxx.com/bg.jpg",
        "content": "{...}",
        "preview_url": "https://xxx.com/preview.jpg",
        "status": "启用",
        "creator": "admin",
        "create_time": "2024-07-01 10:00:00",
        "updater": "admin",
        "update_time": "2024-07-01 10:00:00"
      }
    ]
  }
}
```

## 6. 证书模板预览
- **接口地址**：/api/publicbiz/certificate/template/preview
- **请求方式**：GET
- **入参**：`?id=1`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "preview_url": "https://xxx.com/preview.jpg"
  }
}
```
