# 考题管理接口文档

## 1. 新增考题
- **接口地址**：/api/publicbiz/question/create
- **请求方式**：POST
- **入参**：
```
{
  "level1": "一级分类",
  "level2": "二级分类",
  "level3": "三级分类",
  "cert_name": "认定点名称",
  "title": "题干",
  "type": "单选",
  "biz": "业务模块",
  "answer": "参考答案"
}
```
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": { "id": 1 }
}
```

## 2. 编辑考题
- **接口地址**：/api/publicbiz/question/update
- **请求方式**：POST
- **入参**：同新增，需包含id字段
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 3. 删除考题
- **接口地址**：/api/publicbiz/question/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 4. 考题详情
- **接口地址**：/api/publicbiz/question/detail
- **请求方式**：GET
- **入参**：`?id=1`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "level1": "一级分类",
    "level2": "二级分类",
    "level3": "三级分类",
    "cert_name": "认定点名称",
    "title": "题干",
    "type": "单选",
    "biz": "业务模块",
    "answer": "参考答案",
    "creator": "admin",
    "create_time": "2024-07-01 10:00:00",
    "updater": "admin",
    "update_time": "2024-07-01 10:00:00"
  }
}
```

## 5. 考题分页查询
- **接口地址**：/api/publicbiz/question/page
- **请求方式**：GET
- **入参**：`?page=1&size=10&level1=xxx&type=单选&biz=xxx&keyword=xxx`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "level1": "一级分类",
        "level2": "二级分类",
        "level3": "三级分类",
        "cert_name": "认定点名称",
        "title": "题干",
        "type": "单选",
        "biz": "业务模块",
        "answer": "参考答案",
        "creator": "admin",
        "create_time": "2024-07-01 10:00:00",
        "updater": "admin",
        "update_time": "2024-07-01 10:00:00"
      }
    ]
  }
}
```

## 6. 考题分类相关接口

### 6.1 新增分类
- **接口地址**：/api/publicbiz/question/category/create
- **请求方式**：POST
- **入参**：
```
{
  "level1": "一级分类",
  "level2": "二级分类",
  "level3": "三级分类",
  "biz": "业务模块"
}
```
- **出参**：
```
{ "code": 0, "msg": "success", "data": { "id": 1 } }
```

### 6.2 删除分类
- **接口地址**：/api/publicbiz/question/category/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

### 6.3 查询分类列表
- **接口地址**：/api/publicbiz/question/category/list
- **请求方式**：GET
- **入参**：`?level1=xxx&biz=xxx`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "level1": "一级分类",
      "level2": "二级分类",
      "level3": "三级分类",
      "biz": "业务模块"
    }
  ]
}
```

## 7. 操作日志接口
- 详见通用操作日志接口文档。
