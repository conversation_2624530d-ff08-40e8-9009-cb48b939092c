<!--
  页面名称：数字资产（课程）管理
  功能描述：展示课程列表，支持多条件筛选、分页、上下架、管理课程等操作
-->
<template>
  <div v-if="!showManagementCourse">
    <!-- <el-button type="primary" @click="openAddDrawer" style="margin-bottom: 16px;">新增课程</el-button> -->
    <!-- 顶部统计卡片（样式对齐师资库） -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" v-for="(card, idx) in statCards" :key="card.title">
        <el-card :class="`stat-card stat-bg${idx + 1}`" v-loading="statisticsLoading">
          <div class="stat-card-inner">
            <el-icon :class="`stat-icon stat-icon${idx + 1}`"
              ><component :is="card.icon"
            /></el-icon>
            <div>
              <div class="stat-num">{{ card.value }}</div>
              <div class="stat-label">{{ card.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索区域 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="课程分类：">
        <el-select v-model="searchForm.category" placeholder="全部" style="width: 140px">
          <el-option
            v-for="item in courseCategoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程状态：">
        <el-select v-model="searchForm.status" placeholder="全部" style="width: 140px">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="授课方式：">
        <el-select v-model="searchForm.teachType" placeholder="全部" style="width: 140px">
          <el-option
            v-for="item in teachTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务模块：">
        <el-select v-model="searchForm.businessModule" placeholder="全部" style="width: 140px">
          <el-option
            v-for="item in businessModuleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="收款商户：">
        <el-select v-model="searchForm.merchant" placeholder="全部" style="width: 160px">
          <el-option
            v-for="item in merchantOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索课程名称..."
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">筛选</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格外层容器 -->
    <div class="table-wrapper">
      <!-- 课程信息表格 -->
      <div class="table-responsive">
        <el-table
          :data="tableData"
          style="min-width: 900px; width: 100%"
          border
          :header-cell-style="{ background: '#fafbfc' }"
          v-loading="loading"
        >
          <el-table-column prop="coverUrl" label="课程信息" width="400">
            <template #default="scope">
              <el-image
                :src="scope.row.coverUrl"
                style="width: 60px; height: 40px; margin-right: 8px"
                fit="cover"
              />
              <span>{{ scope.row.name }}</span>
              <div class="course-category">{{ scope.row.category }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="teachType" label="授课方式" width="190">
            <template #default="scope">
              <el-tag :type="scope.row.teachType === '线上授课' ? 'success' : 'warning'">{{
                scope.row.teachType
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="teacherName" label="关联讲师" width="190" />
          <el-table-column prop="businessModule" label="业务模块" width="190" />
          <el-table-column prop="merchantName" label="收款商户" width="190" />
          <el-table-column prop="status" label="状态" width="190">
            <template #default="scope">
              <el-tag
                :type="
                  scope.row.status === '已上架'
                    ? 'success'
                    : scope.row.status === '已下架'
                      ? 'info'
                      : 'warning'
                "
                >{{ scope.row.status }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280">
            <template #default="scope">
              <el-button size="small" @click="onManage(scope.row)">管理课程</el-button>
              <el-button
                size="small"
                type="warning"
                v-if="scope.row.status === '已上架'"
                @click="onDown(scope.row)"
                :loading="statusLoading[scope.row.id] || false"
                :disabled="statusLoading[scope.row.id] || false"
                >下架</el-button
              >
              <el-button
                size="small"
                type="success"
                v-else
                @click="onUp(scope.row)"
                :loading="statusLoading[scope.row.id] || false"
                :disabled="statusLoading[scope.row.id] || false"
                >上架</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="fetchList"
          @size-change="handleSizeChange"
          style="margin-top: 16px; text-align: right"
        />
      </div>
    </div>
    <!-- 保证el-drawer在template根节点下 -->
    <AddDigitalAsset ref="addDigitalAssetRef" @success="handleAddSuccess" />
  </div>
  <div v-else>
    <!-- 根据授课方式显示不同的管理页面 -->
    <ManagementCourse
      v-if="currentCourseRow?.teachType === '线下授课'"
      :course="currentCourseRow"
      @go-back="closeManagementCourse"
    />
    <ManagementCourseForOnline
      v-else-if="currentCourseRow?.teachType === '线上授课'"
      :course="currentCourseRow"
      @go-back="closeManagementCourse"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DigitalAssetApi,
  type CoursePageParams,
  type DigitalAssetCourse,
  type CourseStatisticsResult
} from '@/api/infra/digitalAsset'
import {
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn,
  ElTag,
  ElImage,
  ElPagination,
  ElIcon,
  ElCard
} from 'element-plus'
import { Document, VideoCamera, Monitor, SwitchButton } from '@element-plus/icons-vue'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getAppList } from '@/api/pay/app'
import AddDigitalAsset from './components/AddDigitalAsset.vue'
import ManagementCourse from './components/ManagementCourse.vue'
import ManagementCourseForOnline from './components/ManagementCourseForOnline.vue'

const router = useRouter()
const addDigitalAssetRef = ref()
function openAddDrawer() {
  addDigitalAssetRef.value && addDigitalAssetRef.value.open()
}

function handleAddSuccess(result: any) {
  console.log('课程创建成功:', result)
  // 刷新列表数据和统计数据
  fetchCourseList()
  fetchCourseStatistics()
}
// 顶部统计卡片数据
const statCards = ref([
  { title: '课程总数', value: 0, icon: markRaw(Document) },
  { title: '线上课程', value: 0, icon: markRaw(VideoCamera) },
  { title: '线下课程', value: 0, icon: markRaw(Monitor) },
  { title: '已上架', value: 0, icon: markRaw(SwitchButton) }
])
const statisticsLoading = ref(false)

// 搜索区域下拉框mock数据
const courseCategoryOptions = [
  { label: '全部', value: '' },
  { label: '家政技能', value: '家政技能' },
  { label: '职业素养', value: '职业素养' },
  { label: '高校实践', value: '高校实践' },
  { label: '企业管理', value: '企业管理' }
]
const statusOptions = [
  { label: '全部', value: '' },
  { label: '已上架', value: '已上架' },
  { label: '已下架', value: '已下架' },
  { label: '待发布', value: '待发布' }
]
const teachTypeOptions = [
  { label: '全部', value: '' },
  { label: '线上授课', value: '线上授课' },
  { label: '线下授课', value: '线下授课' }
]
const businessModuleOptions = ref<any[]>([{ label: '全部', value: '' }])
const merchantOptions = ref<any[]>([{ label: '全部', value: '' }])

// 搜索表单数据
const searchForm = ref({
  category: '',
  status: '',
  teachType: '',
  businessModule: '',
  merchant: '',
  keyword: ''
})

// 表格数据和加载状态
const tableData = ref<DigitalAssetCourse[]>([])
const loading = ref(false)
const statusLoading = ref<Record<number, boolean>>({})

// 分页信息
const pagination = ref({ page: 1, size: 10, total: 0 })

// 管理课程嵌入显示控制
const showManagementCourse = ref(false)
const currentCourseRow = ref<any>(null)
function onManage(row: any) {
  console.log('onManage 被调用，row:', row)
  console.log('row.teachType:', row.teachType)
  currentCourseRow.value = row
  showManagementCourse.value = true
  console.log('设置 currentCourseRow:', currentCourseRow.value)
  console.log('设置 showManagementCourse:', showManagementCourse.value)
}
function closeManagementCourse() {
  showManagementCourse.value = false
}

// 获取课程列表数据
const fetchCourseList = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: CoursePageParams = {
      pageNo: pagination.value.page,
      pageSize: pagination.value.size
    }

    // 添加筛选条件
    if (searchForm.value.category) {
      params.category = searchForm.value.category
    }
    if (searchForm.value.status) {
      params.status = searchForm.value.status
    }
    if (searchForm.value.teachType) {
      params.teachType = searchForm.value.teachType
    }
    if (searchForm.value.businessModule) {
      params.businessModule = searchForm.value.businessModule
    }
    if (searchForm.value.merchant) {
      params.merchant = Number(searchForm.value.merchant)
    }
    if (searchForm.value.keyword) {
      params.keyword = searchForm.value.keyword
    }

    // 调用API获取数据
    const result = await DigitalAssetApi.getCoursePage(params)

    tableData.value = result.list
    pagination.value.total = result.total
  } catch (error) {
    console.error('获取课程列表失败:', error)
    ElMessage.error('获取课程列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 搜索方法
const onSearch = () => {
  pagination.value.page = 1 // 重置到第一页
  fetchCourseList()
}

// 分页变更方法
const fetchList = (page?: number) => {
  if (page) {
    pagination.value.page = page
  }
  fetchCourseList()
}

// 页面大小变更方法
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1 // 重置到第一页
  fetchCourseList()
}

// 课程下架
const onDown = async (row: DigitalAssetCourse) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要下架课程"${row.name}"吗？下架后学员将无法继续学习该课程。`,
      '确认下架',
      {
        confirmButtonText: '确定下架',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 设置Loading状态
    if (row.id) {
      statusLoading.value[row.id] = true
    }

    await DigitalAssetApi.updateCourseStatus(row.id!, '已下架')
    ElMessage.success('课程下架成功')
    fetchCourseList() // 刷新列表
    fetchCourseStatistics() // 刷新统计数据
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作，不显示错误信息
      return
    }
    console.error('课程下架失败:', error)
    ElMessage.error('课程下架失败，请重试')
  } finally {
    // 清除Loading状态
    if (row.id) {
      statusLoading.value[row.id] = false
    }
  }
}

// 课程上架
const onUp = async (row: DigitalAssetCourse) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要上架课程"${row.name}"吗？上架后学员将可以报名学习该课程。`,
      '确认上架',
      {
        confirmButtonText: '确定上架',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    // 设置Loading状态
    if (row.id) {
      statusLoading.value[row.id] = true
    }

    await DigitalAssetApi.updateCourseStatus(row.id!, '已上架')
    ElMessage.success('课程上架成功')
    fetchCourseList() // 刷新列表
    fetchCourseStatistics() // 刷新统计数据
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作，不显示错误信息
      return
    }
    console.error('课程上架失败:', error)
    ElMessage.error('课程上架失败，请重试')
  } finally {
    // 清除Loading状态
    if (row.id) {
      statusLoading.value[row.id] = false
    }
  }
}

// 获取课程统计数据
const fetchCourseStatistics = async () => {
  try {
    statisticsLoading.value = true

    const result = await DigitalAssetApi.getCourseStatistics()

    // 更新统计卡片数据
    statCards.value[0].value = result.totalCount
    statCards.value[1].value = result.onlineCount
    statCards.value[2].value = result.offlineCount
    statCards.value[3].value = result.publishedCount
  } catch (error) {
    console.error('获取课程统计数据失败:', error)
    ElMessage.error('获取统计数据失败，请重试')
  } finally {
    statisticsLoading.value = false
  }
}

/** 获取业务模块字典数据 */
const fetchBusinessModuleOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    businessModuleOptions.value = [
      { label: '全部', value: '' },
      ...(res?.list || []).map((item) => ({
        label: item.label,
        value: item.value
      }))
    ]
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

/** 获取商户列表数据 */
const fetchMerchantOptions = async () => {
  try {
    const res = await getAppList()
    console.log('支付应用接口返回数据:', res)

    if (Array.isArray(res)) {
      console.log('数据直接在res中，数组长度:', res.length)
      // 直接使用应用数据作为商户选项
      merchantOptions.value = [
        { label: '全部', value: '' },
        ...res.map((app: any) => ({
          id: app.id,
          name: app.name,
          label: app.name,
          value: app.id
        }))
      ]
      console.log('处理后的商户选项:', merchantOptions.value)
    } else {
      console.log('支付应用接口返回数据格式不符合预期:', res)
    }
  } catch (error) {
    console.error('获取商户列表失败:', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchBusinessModuleOptions() // 获取业务模块数据
  fetchMerchantOptions() // 获取商户数据
  fetchCourseStatistics() // 获取统计数据
  fetchCourseList() // 获取课程列表
})
</script>

<style scoped lang="scss">
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.stat-icon1 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67c23a;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #e6a23c;
  background: #fff5e6;
}
.stat-icon4 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.search-form {
  margin-bottom: 8px;
}
.course-category {
  font-size: 12px;
  color: #888;
}
.table-wrapper {
  background: #fff;
  padding: 0 0 16px 0;
  border-radius: 8px;
}
.table-responsive {
  width: 100%;
  overflow-x: auto;
}
@media (max-width: 1200px) {
  .stat-card-inner {
    gap: 8px;
  }
  .stat-num {
    font-size: 15px;
  }
  .stat-label {
    font-size: 11px;
  }
  .el-table th,
  .el-table td {
    font-size: 13px;
  }
}
@media (max-width: 900px) {
  .stat-card-inner {
    gap: 4px;
  }
  .stat-num {
    font-size: 13px;
  }
  .stat-label {
    font-size: 10px;
  }
  .el-table th,
  .el-table td {
    font-size: 12px;
  }
  .table-responsive {
    min-width: 600px;
  }
}
</style>
