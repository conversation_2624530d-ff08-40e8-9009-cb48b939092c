import request from '@/config/axios'

// 证书模板字段类型
export interface CertificateField {
  id: string
  type: string
  label: string
  x: number
  y: number
  fontSize: number
  color: string
  fontFamily: string
  selected: boolean
}

// 证书模板数据结构
export interface CertificateTemplateVO {
  id?: number
  name: string
  type: string
  description: string
  background: string
  backgroundUrl?: string
  course: string
  status: string
  fields: CertificateField[]
  htmlContent?: string // HTML模板内容
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
}

// 分页查询参数
export interface CertificateTemplatePageParams {
  page?: number
  size?: number
  type?: string
  status?: string
  keyword?: string
}

// 分页查询结果
export interface CertificateTemplatePageResult {
  total: number
  list: CertificateTemplateVO[]
}

// 新增证书模板参数
export interface CreateCertificateTemplateParams {
  name: string
  type: string
  description: string
  background: string
  backgroundUrl?: string
  course: string
  status: string
  fields: CertificateField[]
  htmlContent?: string // HTML模板内容
}

// 更新证书模板参数
export interface UpdateCertificateTemplateParams extends CreateCertificateTemplateParams {
  id: number
}

// 证书模板API
export const CertificateTemplateApi = {
  // 分页查询证书模板
  getPage: async (
    params: CertificateTemplatePageParams
  ): Promise<CertificateTemplatePageResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/page', params })
  },

  // 获取证书模板详情
  getDetail: async (id: number): Promise<CertificateTemplateVO> => {
    return await request.get({ url: `/publicbiz/certificate/template/detail?id=${id}` })
  },

  // 新增证书模板
  create: async (data: CreateCertificateTemplateParams): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/certificate/template/create', data })
  },

  // 更新证书模板
  update: async (data: UpdateCertificateTemplateParams): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/update', data })
  },

  // 删除证书模板
  delete: async (id: number): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/delete', data: { id } })
  },

  // 获取证书模板预览
  getPreview: async (id: number): Promise<{ preview_url: string }> => {
    return await request.get({ url: `/publicbiz/certificate/template/preview?id=${id}` })
  }
}

// Mock数据 - 用于开发测试
export const mockCertificateTemplates: CertificateTemplateVO[] = [
  {
    id: 1,
    name: '新媒体初级培训证书模板',
    type: '培训证书',
    description: '新媒体初级培训证书模板描述',
    background: 'default-bg.jpg',
    backgroundUrl: '',
    course: '新媒体初级培训',
    status: '启用中',
    fields: [
      {
        id: 'field_1',
        type: 'name',
        label: '【学员姓名】',
        x: 350,
        y: 200,
        fontSize: 32,
        color: '#333',
        fontFamily: '微软雅黑',
        selected: false
      },
      {
        id: 'field_2',
        type: 'date',
        label: '【发证日期】',
        x: 350,
        y: 400,
        fontSize: 16,
        color: '#666',
        fontFamily: '微软雅黑',
        selected: false
      }
    ],
    htmlContent: `
      <div class="certificate-template" style="width: 900px; height: 600px; position: relative; background: #fcfcfd; border: 2px dashed #dcdfe6; border-radius: 12px; padding: 48px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="font-size: 32px; font-weight: bold; color: #333; margin-bottom: 20px;">{{studentName}}</div>
        </div>
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="font-size: 22px; font-weight: bold; color: #333;">新媒体初级培训证书</div>
        </div>
        <div style="text-align: center; margin-bottom: 40px;">
          <div style="font-size: 16px; color: #666;">兹证明学员已完成相关培训课程</div>
        </div>
        <div style="text-align: center;">
          <div style="font-size: 16px; color: #222; font-weight: bold;">
            发证日期：{{issueDate}}
          </div>
        </div>
        <div style="position: absolute; bottom: 20px; right: 20px; font-size: 14px; color: #999;">
          证书编号：{{certificateCode}}
        </div>
      </div>
    `,
    creator: 'admin',
    createTime: '2024-08-01 10:00:00',
    updater: 'admin',
    updateTime: '2024-08-01 10:00:00'
  }
]

// 占位符映射配置
export const placeholderMap: Record<string, string> = {
  name: 'studentName',
  code: 'certificateCode',
  id: 'studentId',
  date: 'issueDate'
}

// 默认占位符数据
export const defaultPlaceholderData: Record<string, string> = {
  studentName: '张三',
  certificateCode: 'CERT-2024-001',
  studentId: '123456789012345678',
  issueDate: '2024年8月1日'
}
