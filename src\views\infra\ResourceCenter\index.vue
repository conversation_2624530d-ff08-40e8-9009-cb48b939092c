<!--
  页面名称：资源中心首页
  功能描述：展示合作伙伴统计、筛选、列表，支持搜索、分页、操作
-->
<template>
  <div class="resource-center">
    <!-- 顶部Tab切换和新增按钮同一行 -->
    <div class="resource-header-tabs">
      <el-tabs v-model="activeTab" class="mb-2" @tab-click="handleTabChange">
        <el-tab-pane label="合作伙伴" name="partner" />
        <el-tab-pane label="统一师资库" name="teacher" />
        <el-tab-pane label="数字资产(课程)" name="course" />
        <el-tab-pane label="考题管理" name="exam" />
        <el-tab-pane label="场地资源管理" name="venue" />
        <el-tab-pane label="证书模板" name="certificate" />
      </el-tabs>
      <el-button
        v-if="addBtnConfig.show"
        type="primary"
        class="add-btn"
        @click="addBtnConfig.onClick"
        >{{ addBtnConfig.text }}</el-button
      >
    </div>

    <!-- 内容区：根据tab切换 -->
    <div v-if="activeTab === 'partner'">
      <!-- 顶部统计卡片 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card stat-bg1">
            <div class="stat-card-inner">
              <el-icon class="stat-icon stat-icon1"
                ><i class="el-icon-office-building"></i
              ></el-icon>
              <div>
                <div class="stat-num">{{ stat.partnerTotal }}</div>
                <div class="stat-label">合作伙伴总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card stat-bg2">
            <div class="stat-card-inner">
              <el-icon class="stat-icon stat-icon2"><i class="el-icon-user-solid"></i></el-icon>
              <div>
                <div class="stat-num">{{ stat.partnerActive }}</div>
                <div class="stat-label">合作中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card stat-bg3">
            <div class="stat-card-inner">
              <el-icon class="stat-icon stat-icon3"><i class="el-icon-time"></i></el-icon>
              <div>
                <div class="stat-num">{{ stat.partnerPending }}</div>
                <div class="stat-label">待审核</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card stat-bg4">
            <div class="stat-card-inner">
              <el-icon class="stat-icon stat-icon4"
                ><i class="el-icon-warning-outline"></i
              ></el-icon>
              <div>
                <div class="stat-num">{{ stat.partnerRisk }}</div>
                <div class="stat-label">风险伙伴</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选表单 -->
      <el-form :inline="true" :model="searchForm" class="mb-2" @submit.prevent>
        <el-form-item label="机构类型：">
          <el-select v-model="searchForm.type" placeholder="全部" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option
              v-for="item in orgTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 新增业务模块下拉框 -->
        <el-form-item label="业务模块：">
          <el-select
            v-model="searchForm.bizModule"
            placeholder="全部"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in bizModuleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="合作状态：">
          <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索机构名称/负责人"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">筛选</el-button>
        </el-form-item>
      </el-form>

      <!-- 合作伙伴表格 -->
      <div class="table-responsive">
        <el-table :data="tableData" border style="min-width: 900px; width: 100%" :loading="loading">
          <el-table-column prop="name" label="机构名称" min-width="120">
            <template #default="scope">
              <el-link type="primary">{{ scope.row.name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="机构类型" min-width="80" />
          <!-- 新增业务模块列 -->
          <el-table-column prop="biz" label="业务模块" min-width="100" />
          <el-table-column prop="status" label="合作状态" min-width="90" />
          <el-table-column prop="risk" label="风险等级" min-width="90">
            <template #default="scope">
              <el-tag v-if="scope.row.risk === '低级'" type="success">低级</el-tag>
              <el-tag v-else-if="scope.row.risk === '中级'" type="warning">中级</el-tag>
              <el-tag v-else-if="scope.row.risk === '高级'" type="danger">高级</el-tag>
              <el-tag v-else type="info">{{ scope.row.risk }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ownerName" label="我方负责人" min-width="100" />
          <el-table-column prop="createTime" label="创建时间" min-width="120" />
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="scope">
              <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
              <el-button size="small" @click="onDetail(scope.row)">操作日志</el-button>
              <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 新增合作伙伴抽屉 -->
      <AddPartner ref="addPartnerRef" @success="fetchList" />
      <OptLog ref="optLogRef" />
    </div>
    <TeacherDatabase v-else-if="activeTab === 'teacher'" ref="teacherDbRef" />
    <DigitalAsset v-else-if="activeTab === 'course'" />
    <QuestionManagement v-else-if="activeTab === 'exam'" />
    <SiteManagement v-else-if="activeTab === 'venue'" />
    <CertificateTemplate v-else-if="activeTab === 'certificate'" />
    <!-- 默认 fallback，防止意外的空白页面 -->
    <div v-else class="tab-fallback">
      <el-empty description="页面加载中..." />
    </div>
    <!-- 新增考题抽屉 -->
    <AddQuestion v-if="activeTab === 'exam'" v-model:visible="addQuestionDrawerVisible" />
    <!-- 新增师资抽屉 -->
    <AddDigitalAsset v-if="activeTab === 'course'" v-model:visible="addDigitalAssetDrawerVisible" />
    <!-- 新增师资抽屉 -->
    <AddTeacher v-if="activeTab === 'teacher'" v-model:visible="addTeacherDrawerVisible" />
    <!-- 新增场地抽屉 -->
    <AddSite
      v-if="activeTab === 'venue' && addSiteVisible"
      :visible="addSiteVisible"
      :data="addSiteData || {}"
      @close="closeAddSite"
      @submit="handleAddSiteSubmit"
    />
    <!-- 新增证书模板抽屉 -->
    <AddCertificateTemplate
      v-if="activeTab === 'certificate'"
      v-model:visible="addCertificateTemplateDrawerVisible"
    />
    <!-- 其它Tab可继续扩展 -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { TabsPaneContext } from 'element-plus'
import AddPartner from './components/AddPartner.vue'
import OptLog from './components/OptLog.vue'
import TeacherDatabase from './TeacherDatabase/TeacherDatabase.vue'
import DigitalAsset from './DigitalAsset/DigitalAsset.vue'
import QuestionManagement from './QuestionManagement/QuestionManagement.vue'
import SiteManagement from './SiteManagement/SiteManagement.vue'
import CertificateTemplate from './CertificateTemplate/CertificateTemplate.vue'
import AddQuestion from './QuestionManagement/components/AddQuestion.vue'
import AddDigitalAsset from './DigitalAsset/components/AddDigitalAsset.vue'
import AddTeacher from './TeacherDatabase/components/AddTeacher.vue'
import AddSite from './SiteManagement/components/AddSite.vue'
import AddCertificateTemplate from './CertificateTemplate/components/AddCertificateTemplate.vue'
import {
  getPartnerPage,
  getPartnerStat,
  getPartnerDetail,
  deletePartner
} from '@/api/infra/business/partner'
import { getDictDataPage } from '@/api/system/dict/dict.data'

/** 顶部统计数据 */
const stat = reactive({
  partnerTotal: 0,
  partnerActive: 0,
  partnerPending: 0,
  partnerRisk: 0
})

const fetchStat = async () => {
  try {
    const res = await getPartnerStat()
    stat.partnerTotal = res.partnerTotal || 0
    stat.partnerActive = res.partnerActive || 0
    stat.partnerPending = res.partnerPending || 0
    stat.partnerRisk = res.partnerRisk || 0
  } catch {
    stat.partnerTotal = 0
    stat.partnerActive = 0
    stat.partnerPending = 0
    stat.partnerRisk = 0
  }
}

/** 搜索表单数据 */
const searchForm = reactive({
  type: '',
  bizModule: '', // 新增业务模块字段
  status: '',
  keyword: ''
})

/** 表格数据 */
const tableData = ref<any[]>([])
const loading = ref(false)

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 获取合作伙伴列表（调用后端接口） */
const fetchList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      type: searchForm.type,
      biz: searchForm.bizModule, // 这里用biz
      status: searchForm.status,
      keyword: searchForm.keyword
    }
    const res = await getPartnerPage(params)
    console.log('获取合作伙伴列表响应:', res)

    if (res.list) {
      tableData.value = res.list || []
      pagination.total = res.total || 0
      console.log('表格数据:', tableData.value)
    } else {
      tableData.value = []
      pagination.total = 0
    }
  } catch (e) {
    console.error('获取列表失败:', e)
    tableData.value = []
    pagination.total = 0
  }
  loading.value = false
}

/** 搜索按钮操作 */
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 分页切换 */
const handlePageChange = () => {
  fetchList()
}

const addSiteVisible = ref(false)
const addSiteData = ref({})
const closeAllDrawers = () => {
  addSiteVisible.value = false
  addSiteData.value = {}
  addCertificateTemplateDrawerVisible.value = false
  // 如有其它弹窗/抽屉visible变量，也一并关闭和重置
}

const activeTab = ref('partner')
const handleTabChange = (tab: TabsPaneContext) => {
  // Element Plus 的 @tab-click 事件传递的是 TabsPaneContext 对象
  // 根据官方文档和类型定义，该对象包含 paneName 属性
  const rawTabName = tab?.paneName

  // 将 paneName 转换为字符串，因为它可能是 string | number 类型
  const tabName = rawTabName ? String(rawTabName) : ''

  // 在开发环境下输出调试信息，帮助了解实际的对象结构
  if (process.env.NODE_ENV === 'development') {
    console.log('Tab click event - tab object:', tab)
    console.log('Raw tab name:', rawTabName, 'Converted tab name:', tabName)
  }

  // 防御性编程：确保能获取到 tab 名称
  if (!tabName) {
    console.warn('Tab change event: Unable to extract tab name from TabsPaneContext:', tab)
    return
  }

  // 定义有效的 tab 名称列表
  const validTabs = ['partner', 'teacher', 'course', 'exam', 'venue', 'certificate']

  // 验证 tab 名称是否有效
  if (!validTabs.includes(tabName)) {
    console.warn('Invalid tab name:', tabName, 'Valid tabs:', validTabs)
    return
  }

  // 确保 activeTab 始终与当前选中的 tab 保持同步
  // 即使点击的是当前已选中的tab，也要更新值以确保响应式状态正确
  activeTab.value = tabName

  // 延迟关闭抽屉，避免动画冲突
  setTimeout(() => {
    closeAllDrawers()
  }, 100)
}

const openAddSite = () => {
  addSiteData.value = {}
  addSiteVisible.value = true
}
const closeAddSite = () => {
  addSiteVisible.value = false
  addSiteData.value = {}
}
const handleAddSiteSubmit = (_data: any) => {
  closeAddSite()
  ElMessage.success('保存成功')
}

// 新增资源按钮逻辑
const addPartnerRef = ref()
const openAddPartner = () => {
  addPartnerRef.value && addPartnerRef.value.open()
}

// 新增按钮名称和显示控制
const addBtnConfig = computed(() => {
  switch (activeTab.value) {
    case 'partner':
      return { show: true, text: '新增合作伙伴', onClick: openAddPartner }
    case 'teacher':
      return { show: true, text: '新增师资', onClick: openAddTeacher }
    case 'course':
      return { show: true, text: '新增课程', onClick: openAddDigitalAsset }
    case 'venue':
      return { show: true, text: '新增场地', onClick: openAddSite }
    case 'certificate':
      return { show: true, text: '新建模板', onClick: openAddCertificateTemplate }
    case 'exam':
      return { show: true, text: '新增考题', onClick: openAddQuestion }
    default:
      return { show: false, text: '', onClick: () => {} }
  }
})

// 操作日志抽屉逻辑
const optLogRef = ref()

const addQuestionDrawerVisible = ref(false)

const openAddQuestion = () => {
  addQuestionDrawerVisible.value = true
}

const addDigitalAssetDrawerVisible = ref(false)

const openAddDigitalAsset = () => {
  addDigitalAssetDrawerVisible.value = true
}

const addTeacherDrawerVisible = ref(false)

const openAddTeacher = () => {
  addTeacherDrawerVisible.value = true
}

// 新增证书模板抽屉相关
const addCertificateTemplateDrawerVisible = ref(false)

const openAddCertificateTemplate = () => {
  addCertificateTemplateDrawerVisible.value = true
}

// 表格操作按钮事件处理
const onEdit = async (row: any) => {
  if (!row.id) return
  const res = await getPartnerDetail(row.id)
  console.log('getPartnerDetail 返回值:', res)
  if (res) {
    addPartnerRef.value && addPartnerRef.value.open('edit', res)
  }
}

const onDetail = (row: any) => {
  console.log('操作日志', row)
  if (!row.id) {
    ElMessage.error('数据ID缺失，无法查看操作日志')
    return
  }
  optLogRef.value && optLogRef.value.open(row.name || '合作伙伴', row.id)
}

const onDelete = async (row: any) => {
  console.log('删除行数据:', row)
  if (!row.id) {
    ElMessage.error('数据ID缺失，无法删除')
    return
  }
  try {
    await ElMessageBox.confirm('确定要删除该合作伙伴吗？此操作不可撤销。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await deletePartner(row.id)

    if (res) {
      ElMessage.success('删除成功')
      fetchList()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (e: any) {
    if (e !== 'cancel') {
      ElMessage.error(e?.message || '删除失败')
    }
  }
}

const statusOptions = ref<{ label: string; value: string }[]>([])

const loadStatusOptions = async () => {
  const res = await getDictDataPage({ dictType: 'cooperative_status', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  statusOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

const orgTypeOptions = ref<{ label: string; value: string }[]>([])

const loadOrgTypeOptions = async () => {
  const res = await getDictDataPage({ dictType: 'institutional_type', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  orgTypeOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

const bizModuleOptions = ref<{ label: string; value: string }[]>([])
const loadBizModuleOptions = async () => {
  const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  bizModuleOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

onMounted(() => {
  fetchList()
  loadStatusOptions()
  loadOrgTypeOptions()
  loadBizModuleOptions() // 新增业务模块选项加载
  fetchStat()
})
</script>

<style scoped lang="scss">
.resource-center {
  padding: 16px 24px 24px 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.stat-icon1 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67c23a;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #e6a23c;
  background: #fff5e6;
}
.stat-icon4 {
  color: #f56c6c;
  background: #ffeaea;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #ffeaea 0%, #ffd6d6 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.mb-2 {
  margin-bottom: 12px;
}
.mt-3 {
  margin-top: 24px;
}
// 右上角按钮布局
.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.table-responsive {
  width: 100%;
  overflow-x: auto;
}
.resource-header-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
  margin-top: 0;
}
.add-btn {
  margin-left: 16px;
  height: 38px;
}
.tab-fallback {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #fff;
  border-radius: 8px;
  margin-top: 16px;
}
@media (max-width: 1200px) {
  .stat-card-inner {
    gap: 8px;
  }
  .stat-num {
    font-size: 15px;
  }
  .stat-label {
    font-size: 11px;
  }
  .el-table th,
  .el-table td {
    font-size: 13px;
  }
}
@media (max-width: 900px) {
  .stat-card-inner {
    gap: 4px;
  }
  .stat-num {
    font-size: 13px;
  }
  .stat-label {
    font-size: 10px;
  }
  .el-table th,
  .el-table td {
    font-size: 12px;
  }
  .table-responsive {
    min-width: 600px;
  }
}
</style>
